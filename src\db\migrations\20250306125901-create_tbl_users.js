/** @type {import('sequelize-cli').Migration} */
module.exports = {
	up: (queryInterface, Sequelize) =>
		queryInterface.createTable('tbl_users', {
			id: {
				type: Sequelize.INTEGER,
				allowNull: false,
				primaryKey: true,
				autoIncrement: true,
			},
			first_name: {
				type: Sequelize.STRING(400),
				allowNull: false,
			},
			last_name: {
				type: Sequelize.STRING(400),
				allowNull: true,
			},
			mobile_no: {
				type: Sequelize.STRING(15),
				allowNull: true,
			},
			otp: {
				type: Sequelize.STRING(6),
				allowNull: true,
			},
			unique_id: {
				type: Sequelize.TEXT,
				allowNull: false,
			},
			profile_image: {
				type: Sequelize.STRING(400),
				allowNull: true,
			},
			profile_image_path: {
				type: Sequelize.TEXT,
				allowNull: true,
			},
			primary_email: {
				type: Sequelize.STRING(400),
				allowNull: false,
				unique: true,
			},
			password: {
				type: Sequelize.TEXT,
				allowNull: true,
			},
			token: {
				type: Sequelize.TEXT,
				allowNull: true,
			},
			device_token: {
				type: Sequelize.TEXT,
				allowNull: true,
			},
			wo_role_id: {
				type: Sequelize.SMALLINT,
				allowNull: false,
			},
			home_owner_id: {
				type: Sequelize.INTEGER,
				allowNull: true,
			},
			dealer_id: {
				type: Sequelize.INTEGER,
				allowNull: true,
			},
			two_step_verification: {
				type: Sequelize.BOOLEAN,
				defaultValue: false,
			},
			is_contact: {
				type: Sequelize.BOOLEAN,
				defaultValue: false,
			},
			login_access: {
				type: Sequelize.BOOLEAN,
				defaultValue: true,
			},
			is_active: {
				type: Sequelize.BOOLEAN,
				defaultValue: true,
			},
			created_date: {
				type: Sequelize.DATE,
				defaultValue: Sequelize.NOW,
				allowNull: false,
			},
			created_by: {
				type: Sequelize.INTEGER,
				allowNull: true,
			},
			modified_date: {
				type: Sequelize.DATE,
				defaultValue: Sequelize.NOW,
				allowNull: true,
			},
			modified_by: {
				type: Sequelize.INTEGER,
				allowNull: true,
			},
			is_deleted: {
				type: Sequelize.BOOLEAN,
				defaultValue: false,
			},
			deleted_by: {
				type: Sequelize.INTEGER,
				allowNull: true,
			},
			deleted_date: {
				type: Sequelize.DATE,
				allowNull: true,
			},
			dealer_role_id: {
				type: Sequelize.SMALLINT,
				allowNull: true,
			},
		}),
	down: (queryInterface /* , Sequelize */) =>
		queryInterface.dropTable('tbl_users'),
};
