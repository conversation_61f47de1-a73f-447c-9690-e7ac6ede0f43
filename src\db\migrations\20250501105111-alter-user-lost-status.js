/** @type {import('sequelize-cli').Migration} */
module.exports = {
	up: async (queryInterface, Sequelize) => {
		const tableDescription = await queryInterface.describeTable('tbl_users');

		if (!tableDescription.lost_status_id) {
			await queryInterface.addColumn('tbl_users', 'lost_status_id', {
				type: Sequelize.INTEGER,
				allowNull: true,
			});
		}

		if (!tableDescription.lost_status_comments) {
			await queryInterface.addColumn('tbl_users', 'lost_status_comments', {
				type: Sequelize.TEXT,
				allowNull: true,
			});
		}
	},

	down: async (queryInterface) => {
		const tableDescription = await queryInterface.describeTable('tbl_users');

		if (tableDescription.lost_status_id) {
			await queryInterface.removeColumn('tbl_users', 'lost_status_id');
		}

		if (tableDescription.lost_status_comments) {
			await queryInterface.removeColumn('tbl_users', 'lost_status_comments');
		}
	},
};
