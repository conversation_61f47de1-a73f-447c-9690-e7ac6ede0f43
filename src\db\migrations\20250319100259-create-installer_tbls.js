/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up(queryInterface, Sequelize) {
		await queryInterface.createTable('tbl_installers', {
			id: {
				allowNull: false,
				autoIncrement: true,
				primaryKey: true,
				type: Sequelize.INTEGER,
			},
			name: {
				type: Sequelize.STRING,
			},
			email: {
				type: Sequelize.STRING,
				unique: true, // Email is unique
			},
			phone: {
				type: Sequelize.STRING,
				unique: true, // Phone is unique
			},
			dealer_id: {
				type: Sequelize.INTEGER,
			},
			status: {
				type: Sequelize.INTEGER,
				defaultValue: 1, // Default '1' for active, '0' for inactive
				comment: '1 = Active, 0 = Inactive',
			},
			is_deleted: {
				type: Sequelize.BOOLEAN,
				defaultValue: false,
			},
			deleted_by: {
				type: Sequelize.INTEGER,
			},
			deleted_date: {
				type: Sequelize.DATE,
			},
			created_by: {
				type: Sequelize.INTEGER,
			},
			created_date: {
				type: Sequelize.DATE,
			},
			updated_by: {
				type: Sequelize.INTEGER,
			},
			updated_date: {
				type: Sequelize.DATE,
			},
		});
	},
	async down(queryInterface) {
		await queryInterface.dropTable('tbl_installers');
	},
};
