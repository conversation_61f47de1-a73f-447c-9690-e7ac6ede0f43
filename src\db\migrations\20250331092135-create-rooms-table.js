/** @type {import('sequelize-cli').Migration} */
module.exports = {
	up: async (queryInterface, Sequelize) => {
		await queryInterface.createTable('tbl_rooms', {
			id: {
				type: Sequelize.INTEGER,
				allowNull: false,
				primaryKey: true,
				autoIncrement: true,
			},
			user_id: {
				type: Sequelize.STRING,
				allowNull: false,
			},
			room_name: {
				type: Sequelize.STRING,
				allowNull: false,
			},
			is_delete: {
				type: Sequelize.BOOLEAN,
				defaultValue: false,
			},
			created_by: {
				type: Sequelize.STRING,
				allowNull: true,
			},
			created_date: {
				type: Sequelize.DATE,
				defaultValue: Sequelize.NOW,
			},
			updated_date: {
				type: Sequelize.DATE,
				defaultValue: Sequelize.NOW,
			},
		});
	},

	down: async (queryInterface /* , Sequelize */) => {
		await queryInterface.dropTable('tbl_rooms');
	},
};
