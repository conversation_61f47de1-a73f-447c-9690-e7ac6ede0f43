const Joi = require('@hapi/joi');

const getHomeownerList = {
	query: Joi.object().keys({
		limit: Joi.number().integer().min(1).max(100).default(10).messages({
			'number.base': 'Limit must be a number',
			'number.min': 'Limit must be at least 1',
			'number.max': 'Limit cannot exceed 100',
		}),
		page: Joi.number().integer().min(1).default(1).messages({
			'number.base': 'Page must be a number',
			'number.min': 'Page must be at least 1',
		}),
		order_by: Joi.string()
			.valid(
				'id',
				'created_at',
				'first_name',
				'last_name',
				'email_id',
				'mobile_no'
			)
			.default('created_at')
			.messages({
				'any.only':
					'Sort by field must be one of: id,created_at, first_name, last_name, email_id, mobile_no',
			}),
		sort: Joi.string()
			.valid('ASC', 'DESC', 'asc', 'desc')
			.default('DESC')
			.messages({
				'any.only': 'Sort order must be either ASC or DESC',
			}),
		search: Joi.string().allow('').trim().messages({
			'string.base': 'Search term must be a string',
		}),
		status: Joi.number().valid(0, 1).messages({
			'any.only': 'Status must be either 0 (active) or 1 (inactive)',
		}),
	}),
};

const getHomeownerById = {
	params: Joi.object().keys({
		id: Joi.number().integer().required().messages({
			'number.base': 'ID must be a number',
			'any.required': 'ID is required',
		}),
	}),
};
const deleteHomeowner = {
	params: Joi.object().keys({
		id: Joi.number().integer().required(),
	}),
};
// Add other validation schemas as needed (create, update, delete)
const createHomeowner = {
	body: Joi.object().keys({
		first_name: Joi.string().required().max(255).messages({
			'string.empty': 'First name is required',
			'any.required': 'First name is required',
		}),
		last_name: Joi.string().allow('', null).optional().max(255).messages({
			'string.empty': 'Last name is required',
			'any.required': 'Last name is required',
		}),
		primary_email: Joi.string().required().email().max(255).messages({
			'string.email': 'Please provide a valid email address',
			'any.required': 'Email is required',
		}),
		mobile_no: Joi.string()
			.pattern(/^\d{10}$/)
			.required()
			.messages({
				'string.pattern.base': 'Mobile number must be 10 digits',
				'any.required': 'Mobile number is required',
			}),
		address: Joi.string().allow('', null).optional().max(500).messages({
			'string.max': 'Address must not exceed 500 characters',
		}),
		city: Joi.string().allow('', null).optional().max(100).messages({
			'string.max': 'City must not exceed 100 characters',
		}),
		state: Joi.string().allow('', null).optional().max(100).messages({
			'string.max': 'State must not exceed 100 characters',
		}),
		country: Joi.string().allow('', null).optional().max(100).messages({
			'string.max': 'Country must not exceed 100 characters',
		}),
		zip: Joi.string()
			.allow('', null)
			.pattern(/^[0-9]{5}(-[0-9]{4})?$/)
			.optional()
			.messages({
				'string.pattern.base': 'ZIP code must be in format 12345 or 12345-6789',
			}),
	}),
};

const updateHomeowner = {
	params: Joi.object().keys({
		id: Joi.number().required(),
	}),
	body: Joi.object()
		.keys({
			first_name: Joi.string().required().max(255).messages({
				'string.empty': 'First name is required',
				'any.required': 'First name is required',
			}),
			last_name: Joi.string().min(2).max(50),
			primary_email: Joi.string().required().email().max(255).messages({
				'string.email': 'Email must be a valid email address',
				'string.empty': 'Email ID is required',
				'any.required': 'Email ID is required',
			}),
			mobile_no: Joi.string()
				.pattern(/^[0-9]+$/) // Only allow numbers
				.required()
				.messages({
					'string.pattern.base': 'Mobile number must contain only digits',
					'string.empty': 'Mobile number is required',
					'any.required': 'Mobile number is required',
				}),
			address: Joi.string().allow('', null).optional().max(500).messages({
				'string.max': 'Address must not exceed 500 characters',
			}),
			city: Joi.string().allow('', null).optional().max(100).messages({
				'string.max': 'City must not exceed 100 characters',
			}),
			state: Joi.string().allow('', null).optional().max(100).messages({
				'string.max': 'State must not exceed 100 characters',
			}),
			country: Joi.string().allow('', null).optional().max(100).messages({
				'string.max': 'Country must not exceed 100 characters',
			}),
			zip: Joi.string()
				.allow('', null)
				.pattern(/^[0-9]{5}(-[0-9]{4})?$/)
				.optional()
				.messages({
					'string.pattern.base':
						'Zip code must be in the format 12345 or 12345-6789',
				}),
		})
		.min(1), // At least one field should be present for update
};

const importHomeowners = {
	file: Joi.object({
		fieldname: Joi.string().required(),
		originalname: Joi.string().required(),
		encoding: Joi.string().required(),
		mimetype: Joi.string()
			.valid(
				'application/vnd.ms-excel',
				'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
				'text/csv'
			)
			.required(),
		buffer: Joi.binary().required(),
		size: Joi.number()
			.max(5 * 1024 * 1024)
			.required(), // 5MB max
	}).required(),
	body: Joi.object().keys({
		homeowners: Joi.array().items(
			Joi.object({
				firstName: Joi.string().required().trim().messages({
					'string.empty': 'First name is required',
					'any.required': 'First name is required',
				}),
				lastName: Joi.string().optional().trim(),
				mobileNo: Joi.string()
					.pattern(/^\d{10}$/)
					.required()
					.messages({
						'string.pattern.base': 'Mobile number must be 10 digits',
						'any.required': 'Mobile number is required',
					}),
				emailId: Joi.string().email().required().messages({
					'string.email': 'Please provide a valid email address',
					'any.required': 'Email is required',
				}),
				role_id: Joi.number().valid(2).default(2),
				address: Joi.string().optional(),
				city: Joi.string().optional(),
				state: Joi.string().optional(),
				country: Joi.string().optional(),
				zip: Joi.string().optional(),
			})
		),
	}),
};

module.exports = {
	getHomeownerList,
	getHomeownerById,
	createHomeowner,
	deleteHomeowner,
	importHomeowners,
	updateHomeowner,
};
