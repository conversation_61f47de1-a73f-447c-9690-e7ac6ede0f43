// const { Client } = require('pg');
// const config = require('./config');
// const logger = require('./logger');

// let client;

// (async function name() {
// 	console.log("config at postgres.js:",config.sqlDB);
// 	client = new Client(config.sqlDB);
// 	try {
// 		await client.connect();
// 		logger.info('Connect to postgress sucessfully');
// 		return client;
// 	} catch (error) {
// 		logger.error('Connect to postgress error');
// 		process.exit(1);
// 	}
// })();

// module.exports = {
// 	postgres: client,
// };
// const { Client } = require('pg');
// const config = require('./config');
// const logger = require('./logger');

// let client;

// (async function connectToPostgres() {
// 	console.log("config at postgres.js:", config.sqlDB);

// 	// Adjust the config object for pg.Client
// 	const dbConfig = {
// 		user: config.sqlDB.username,  // ✅ Rename username to user
// 		host: config.sqlDB.host,
// 		database: config.sqlDB.database,
// 		password: config.sqlDB.password,
// 		port: 5432, // Ensure the correct port is used
// 	};

// 	client = new Client(dbConfig);
// 	try {
// 		await client.connect();
// 		logger.info('Connected to PostgreSQL successfully');
// 	} catch (error) {
// 		logger.error('Connect to PostgreSQL error:', error);
// 		process.exit(1);
// 	}
// })();

// module.exports = {
// 	postgres: client,
// };

// sequelize.js
const { Sequelize } = require('sequelize');
const config = require('./config');
const logger = require('./logger');

// Create a new Sequelize instance
const sequelize = new Sequelize(
	config.sqlDB.database,
	config.sqlDB.username,
	config.sqlDB.password,
	{
		host: config.sqlDB.host,
		dialect: config.sqlDB.dialect,
		port: config.sqlDB.port,
		pool: {
			max: config.sqlDB.pool.max || 5,
			min: config.sqlDB.pool.min || 0,
			acquire: config.sqlDB.pool.acquire || 30000,
			idle: config.sqlDB.pool.idle || 10000,
		},
		define: {
			timestamps: false,
			freezeTableName: true,
			underscored: true,
		},
		logging: (msg) => logger.info(msg), // Use your logger for Sequelize logs
	}
);

// Function to test the database connection
async function dbConnection() {
	try {
		await sequelize.authenticate();
		logger.info('Connection to PostgreSQL has been established successfully.');
	} catch (error) {
		logger.error('Unable to connect to the database:', error);
		process.exit(1); // Exit the process with failure
	}
}

dbConnection();

module.exports = sequelize;
