/** @type {import('sequelize-cli').Migration} */
module.exports = {
	up: async (queryInterface) => {
		const checkConstraintExists = async (constraintName) => {
			const [results] = await queryInterface.sequelize.query(
				`SELECT conname FROM pg_constraint WHERE conname = '${constraintName}';`
			);
			return results.length > 0;
		};

		if (!(await checkConstraintExists('User_Dealer_ForeignKey'))) {
			await queryInterface.addConstraint('tbl_users', {
				fields: ['dealer_id'],
				type: 'foreign key',
				name: 'User_Dealer_ForeignKey',
				references: {
					table: 'tbl_dealers',
					field: 'id',
				},
				onUpdate: 'NO ACTION',
				onDelete: 'NO ACTION',
				validate: false,
			});
		}

		if (!(await checkConstraintExists('HO_user_Fkey'))) {
			await queryInterface.addConstraint('tbl_users', {
				fields: ['home_owner_id'],
				type: 'foreign key',
				name: 'HO_user_Fkey',
				references: {
					table: 'tbl_home_owners',
					field: 'id',
				},
				onUpdate: 'NO ACTION',
				onDelete: 'NO ACTION',
				validate: false,
			});
		}

		if (!(await checkConstraintExists('Map WO role with users'))) {
			await queryInterface.addConstraint('tbl_users', {
				fields: ['wo_role_id'],
				type: 'foreign key',
				name: 'Map WO role with users',
				references: {
					table: 'tbl_wo_roles',
					field: 'id',
				},
				onUpdate: 'NO ACTION',
				onDelete: 'NO ACTION',
				validate: false,
			});
		}

		if (!(await checkConstraintExists('Dealer_log_fkey'))) {
			await queryInterface.addConstraint('tbl_dealer_status_log', {
				fields: ['dealer_id'],
				type: 'foreign key',
				name: 'Dealer_log_fkey',
				references: {
					table: 'tbl_dealers',
					field: 'id',
				},
				onUpdate: 'NO ACTION',
				onDelete: 'NO ACTION',
				validate: false,
			});
		}

		if (!(await checkConstraintExists('Dealer_Role_Fk'))) {
			await queryInterface.addConstraint('tbl_dealer_roles', {
				fields: ['dealer_id'],
				type: 'foreign key',
				name: 'Dealer_Role_Fk',
				references: {
					table: 'tbl_dealers',
					field: 'id',
				},
				onUpdate: 'NO ACTION',
				onDelete: 'NO ACTION',
				validate: false,
			});
		}
	},

	down: async (queryInterface) => {
		const removeConstraintIfExists = async (tableName, constraintName) => {
			try {
				await queryInterface.removeConstraint(tableName, constraintName);
			} catch (error) {
				// console.warn(
				// 	`Constraint ${constraintName} does not exist or cannot be removed.`
				// );
			}
		};

		await removeConstraintIfExists('tbl_users', 'User_Dealer_ForeignKey');
		await removeConstraintIfExists('tbl_users', 'HO_user_Fkey');
		await removeConstraintIfExists('tbl_users', 'Map WO role with users');
		await removeConstraintIfExists('tbl_dealer_status_log', 'Dealer_log_fkey');
		await removeConstraintIfExists('tbl_dealer_roles', 'Dealer_Role_Fk');
	},
};
