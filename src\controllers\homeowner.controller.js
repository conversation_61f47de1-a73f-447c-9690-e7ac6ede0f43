const httpStatus = require('http-status');
const xlsx = require('xlsx');
const catchAsync = require('../utils/catchAsync');
const { homeownerService } = require('../services');
const ApiError = require('../utils/ApiError');
const logger = require('../config/logger');

/**
 * Get list of homeowners
 * @route GET /api/homeowner/list
 * @access Admin only
 */
const getHomeownerList = catchAsync(async (req, res) => {
	// console.log("req:",req);
	// Get user from request - could be in req.user or req.auth depending on JWT middleware

	const user = req.auth || req.user;
	if (!user || !user.userId) {
		throw new ApiError(httpStatus.UNAUTHORIZED, 'User not authenticated');
	}

	logger.info(`User ${user.userId} requesting homeowner list`);

	// Extract query parameters
	const {
		limit,
		page,
		sortBy = 'created_at',
		sortOrder,
		search,
		status,
	} = req.query;

	// Get homeowner list with pagination and filters
	const result = await homeownerService.getHomeownerList({
		limit,
		page,
		sortBy,
		sortOrder,
		search,
		status,
		userRole: user.role, // Pass user role
		userId: user.userId, // Pass user ID
	});

	logger.info(
		`Successfully retrieved ${result.data.length} homeowners for user ${user.userId}`
	);

	// Send response
	res.status(httpStatus.OK).json({
		status: httpStatus.OK,
		success: true,
		message: result.data.length
			? 'Homeowner list retrieved successfully'
			: 'No homeowners found',
		data: result.data,
		pagination: result.pagination,
	});
});

/**
 * Get homeowner by ID
 * @route GET /api/homeowner/:id
 * @access Admin only
 */
const getHomeownerById = catchAsync(async (req, res) => {
	// console.log(req);
	const { id } = req.params;
	const user = req.auth || req.user;

	const userRole = user.role; // Pass user role
	const { userId } = user; // Pass user ID

	// Check if user is trying to access their own record
	if (user.userId === parseInt(id, 10)) {
		// Allow access to their own record
		const homeowner = await homeownerService.getHomeownerById(
			id,
			userRole,
			userId
		);
		return res.status(httpStatus.OK).json({
			status: httpStatus.OK,
			success: true,
			message: 'Homeowner retrieved successfully',
			data: homeowner,
		});
	}

	const homeowner = await homeownerService.getHomeownerById(
		id,
		userRole,
		userId
	);

	res.status(httpStatus.OK).json({
		status: httpStatus.OK,
		success: true,
		message: 'Homeowner retrieved successfully',
		data: homeowner,
	});
});

// Function to convert JSON to CSV (basic implementation)
const convertToCSV = (data) => {
	const header = `${Object.keys(data[0]).join(',')}\n`;
	const rows = data.map((row) => Object.values(row).join(',')).join('\n');
	return header + rows;
};

const exportHomeownerList = catchAsync(async (req, res) => {
	const { user } = req;

	// Get homeowner list
	const homeowners = await homeownerService.exportHomeownerList({
		// limit: 1000, // You can set a high limit for export
		// page: 1, // Only fetch the first page for export
		// sortBy: 'created_at',
		// sortOrder: 'DESC',
		userRole: user.role, // Pass user role
		userId: user.userId, // Pass user ID
	});
	// Format the data for export (e.g., CSV)
	const csvData = homeowners.map((homeowner) => ({
		id: homeowner.id,
		first_name: homeowner.first_name,
		last_name: homeowner.last_name,
		primary_email: homeowner.primary_email,
		mobile_no: homeowner.mobile_no,
		status: homeowner.status,
		created_at: homeowner.created_at,
		updated_at: homeowner.updated_at,
	}));

	// Convert to CSV format (you can use a library like json2csv)
	const csv = convertToCSV(csvData); // Implement this function or use a library

	// Set response headers for CSV download
	res.header('Content-Type', 'text/csv');
	res.attachment('homeowners.csv');
	res.send(csv);
});

const deleteHomeowner = catchAsync(async (req, res) => {
	const { id } = req.params;
	const { userId } = req.user; // Get the logged-in user's ID for tracking who deleted

	await homeownerService.deleteHomeowner(id, userId);
	res.status(httpStatus.OK).json({
		status: httpStatus.OK,
		success: true,
		message: 'Homeowner deleted successfully',
	});
	// res.status(res, httpStatus.OK, 'Homeowner deleted successfully');
});

const importHomeowners = catchAsync(async (req, res) => {
	if (!req.file) {
		throw new ApiError(httpStatus.BAD_REQUEST, 'Please upload a file');
	}

	const { buffer } = req.file;
	const workbook = xlsx.read(buffer, { type: 'buffer' });
	const worksheet = workbook.Sheets[workbook.SheetNames[0]];
	const data = xlsx.utils.sheet_to_json(worksheet);

	if (!data.length) {
		throw new ApiError(httpStatus.BAD_REQUEST, 'File is empty');
	}

	// Validate required columns
	const requiredColumns = ['first_name', 'primary_email', 'mobile_no'];
	const fileColumns = Object.keys(data[0]);
	const missingColumns = requiredColumns.filter(
		(col) => !fileColumns.includes(col)
	);

	if (missingColumns.length) {
		throw new ApiError(
			httpStatus.BAD_REQUEST,
			`Missing required columns: ${missingColumns.join(', ')}`
		);
	}

	const result = await homeownerService.importHomeowners(data, req);

	res.status(httpStatus.OK).json({
		status: httpStatus.OK,
		success: true,
		message: `Homeowners import completed: ${result.successful_count} successful, ${result.failed} failed`,
		data: {
			total: data.length,
			successful: result.successful.length,
			failed: result.failures.length,
			failures: result.failures,
			imported: result.successful,
		},
	});
});

const addHomeowner = catchAsync(async (req, res) => {
	const roleId = req.user.role;
	const homeownerData = req.body;
	let msg = '';

	const newHomeowner = await homeownerService.createHomeowner(
		homeownerData,
		req
	);
	if (roleId === 1) {
		msg =
			'Homeowner created successfully. Login credentials AND activation link have been sent to their email.';
	} else {
		msg = 'Homeowner created successfully';
	}
	res.status(httpStatus.CREATED).json({
		status: true,
		message: msg,
		data: newHomeowner,
	});
});

/**
 * Update homeowner information
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateHomeowner = catchAsync(async (req, res) => {
	const { id } = req.params;
	const updateData = req.body;
	const { userId } = req.user;

	const updatedHomeowner = await homeownerService.updateHomeowner(
		id,
		updateData,
		userId
	);

	res.status(httpStatus.OK).json({
		status: true,
		message: 'Homeowner information updated successfully',
		data: {
			id: updatedHomeowner.id,
			first_name: updatedHomeowner.first_name,
			last_name: updatedHomeowner.last_name,
			primary_email: updatedHomeowner.primary_email,
			mobile_no: updatedHomeowner.mobile_no,
			address: updatedHomeowner.address,
			city: updatedHomeowner.city,
			state: updatedHomeowner.state,
			country: updatedHomeowner.country,
			zip: updatedHomeowner.zip,
			last_modified: updatedHomeowner.modified_date,
		},
	});
});

module.exports = {
	getHomeownerList,
	getHomeownerById,
	exportHomeownerList,
	deleteHomeowner,
	importHomeowners,
	addHomeowner,
	updateHomeowner,
};
