/** @type {import('sequelize-cli').Migration} */
module.exports = {
	up: async (queryInterface, Sequelize) => {
		const tableDescription = await queryInterface.describeTable('tbl_users');
		if (!tableDescription.activation_token) {
			await queryInterface.addColumn('tbl_users', 'activation_token', {
				type: Sequelize.STRING,
				allowNull: true,
			});
		}
		if (!tableDescription.activation_expires) {
			await queryInterface.addColumn('tbl_users', 'activation_expires', {
				type: Sequelize.DATE,
				allowNull: true,
			});
		}
	},

	down: async (queryInterface) => {
		const tableDescription = await queryInterface.describeTable('tbl_users');
		if (!tableDescription.activation_token) {
			await queryInterface.removeColumn('tbl_users', 'activation_token');
		}
		if (!tableDescription.activation_expires) {
			await queryInterface.removeColumn('tbl_users', 'activation_expires');
		}
	},
};
