/** @type {import('sequelize-cli').Migration} */
module.exports = {
	up: async (queryInterface, Sequelize) => {
		const tableDescription = await queryInterface.describeTable('tbl_users');

		if (!tableDescription.reset_password_token) {
			await queryInterface.addColumn('tbl_users', 'reset_password_token', {
				type: Sequelize.STRING,
				allowNull: true,
			});
		}

		if (!tableDescription.reset_password_expires) {
			await queryInterface.addColumn('tbl_users', 'reset_password_expires', {
				type: Sequelize.DATE,
				allowNull: true,
			});
		}
	},

	down: async (queryInterface) => {
		const tableDescription = await queryInterface.describeTable('tbl_users');

		if (tableDescription.reset_password_token) {
			await queryInterface.removeColumn('tbl_users', 'reset_password_token');
		}

		if (tableDescription.reset_password_expires) {
			await queryInterface.removeColumn('tbl_users', 'reset_password_expires');
		}
	},
};
