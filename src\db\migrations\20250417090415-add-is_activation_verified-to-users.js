/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up(queryInterface, Sequelize) {
		const tableDescription = await queryInterface.describeTable('tbl_users');

		if (!tableDescription.is_activation_verified) {
			await queryInterface.addColumn('tbl_users', 'is_activation_verified', {
				type: Sequelize.BOOLEAN,
				allowNull: false,
				defaultValue: false,
				comment: 'true = verified, false = not verified',
			});
		}
	},

	async down(queryInterface) {
		const tableDescription = await queryInterface.describeTable('tbl_users');

		if (tableDescription.is_activation_verified) {
			await queryInterface.removeColumn('tbl_users', 'is_activation_verified');
		}
	},
};
