const capitalize = (text) => {
	return text.charAt(0).toUpperCase() + text.slice(1);
};

const RESPONSE = {
	// Success Messages
	FETCH_SUCCESS: (entity = 'data') =>
		`${capitalize(entity)} retrieved successfully.`,
	CREATE_SUCCESS: (entity = 'data') =>
		`${capitalize(entity)} created successfully.`,
	UPDATE_SUCCESS: (entity = 'data') =>
		`${capitalize(entity)} updated successfully.`,
	DELETE_SUCCESS: (entity = 'data') =>
		`${capitalize(entity)} deleted successfully.`,

	IMPORT_SUCCESS: (entity = 'file') =>
		`${capitalize(entity)} imported successfully.`,
	EXPORT_SUCCESS: (entity = 'file') =>
		`${capitalize(entity)} exported successfully.`,

	UPLOAD_SUCCESS: (entity = 'file') =>
		`${capitalize(entity)} uploaded successfully.`,

	// Auth
	LOGIN_SUCCESS: 'Logged in successfully.',
	LOGOUT_SUCCESS: 'Logged out successfully.',
	PASSWORD_RESET_SUCCESS: 'Your password has been reset successfully.',

	EMAIL_SENT_SUCCESS: (context = 'verification') =>
		`${capitalize(context)} email sent successfully.`,
	ACTION_SUCCESS: (action = 'Action') =>
		`${capitalize(action)} completed successfully.`,

	// Warning / Info
	NO_CHANGE: (entity = 'data') => `No changes detected in the ${entity}.`,
	ALREADY_EXISTS: (entity = 'record') =>
		`${capitalize(entity)} already exists.`,
	REQUIRED: (entity = 'field') => `${capitalize(entity)} is required.`,
	ALREADY_PROCESSED: (entity = 'record') =>
		`${capitalize(entity)} is already processed.`,

	// Error Messages
	BAD_REQUEST: "Oops! Something's not right. Please check your input.",
	NOT_FOUND: (entity = 'record') => `${capitalize(entity)} not found.`,
	DUPLICATE_FOUND: (entity = 'record') =>
		`${capitalize(entity)} already exists.`,
	VALIDATION_ERROR: 'Validation failed. Please check the entered details.',
	UNAUTHORIZED: 'You are not authorized to perform this action.',
	FORBIDDEN: 'Access denied. Please contact support.',
	INTERNAL_ERROR: 'An unexpected error occurred. Please try again later.',
	SESSION_EXPIRED: 'Your session has expired. Please log in again.',
	INVALID_CREDENTIALS: 'Invalid email or password.',
	TOKEN_EXPIRED: 'This link has expired. Please request a new one.',

	// Customizable
	OPERATION_FAILED: (operation = 'Operation') =>
		`${capitalize(operation)} failed. Please try again.`,
	OPERATION_SUCCESS: (operation = 'Operation') =>
		`${capitalize(operation)} completed successfully.`,
};

module.exports = RESPONSE;
