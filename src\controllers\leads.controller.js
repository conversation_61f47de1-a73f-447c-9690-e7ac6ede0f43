const httpStatus = require('http-status');
const leadsServices = require('../services/leads.service');
const catchAsync = require('../utils/catchAsync');
const ApiSuccess = require('../utils/ApiSuccess');
const ApiError = require('../utils/ApiError');
/**
 * Controller to handle lead creation
 *
 * @route POST /add-lead
 * @access Private (requires authentication)
 */
const addLeads = catchAsync(async (req, res) => {
	// Extract the userId from the authenticated request
	const userId = req.user?.userId;
	// Create a new lead using the request body and authenticated userId
	const newLead = await leadsServices.createLeads(req.body, userId);

	// Send success response with the created lead data
	return new ApiSuccess(
		res,
		httpStatus.OK,
		'Lead created successfully.',
		newLead
	);
});

/**
 * Controller to update lead details
 *
 * @route PUT /update-lead/:id
 * @access Private (requires authentication)
 */
const updateAllLeads = catchAsync(async (req, res) => {
	// Extract user ID from the authenticated request
	const userId = req.user?.userId;
	// Extract lead ID from request parameters
	const { id } = req.params;

	// Call the service to update the lead with the new data
	const updatedLeads = await leadsServices.updateLeads(id, req.body, userId);

	// Return a success response with the updated lead data
	return new ApiSuccess(
		res,
		httpStatus.OK,
		'Lead updated successfully.',
		updatedLeads
	);
});

/**
 * Controller to fetch all leads with optional filters, pagination, and sorting
 *
 * @route GET /get-all-leads
 * @access Private (requires authentication)
 */
const getAllLeads = catchAsync(async (req, res) => {
	// Extract user ID from the authenticated request
	// Fetch leads data using query parameters (e.g., search, pagination, filters)
	const { data, pagination } = await leadsServices.getLeads(req.query);

	// Return success response with fetched lead data
	return new ApiSuccess(
		res,
		httpStatus.OK,
		'Leads fetched successfully',
		data,
		{ pagination }
	);
});

/**
 * Controller to fetch detailed information of a specific lead by ID
 *
 * @route GET /get-lead-details/:id
 * @access Private (requires authentication)
 */
const getLeadDetailsById = catchAsync(async (req, res) => {
	// Extract lead ID from request parameters
	const { id } = req.params;

	// Fetch lead details using the service layer
	const lead = await leadsServices.getLeadById(id);

	// Return successful response with the retrieved lead details
	return new ApiSuccess(
		res,
		httpStatus.OK,
		'Lead details fetched successfully',
		lead
	);
});

/**
 * Controller to soft delete a specific lead by ID
 *
 * @route DELETE /delete-lead/:id
 * @access Private (requires authentication)
 */
const deleteLeadById = catchAsync(async (req, res) => {
	// Extract the authenticated user's ID
	const userId = req.user?.userId;

	// Extract lead ID from request parameters
	const { id } = req.params;

	// Validate the lead ID
	if (!id) {
		return res.status(400).json({
			status: false,
			message: 'Lead ID is required.',
		});
	}

	// Perform soft delete operation using service layer
	await leadsServices.deleteLeadsByID(id, userId);

	// Return successful response
	return new ApiSuccess(res, httpStatus.OK, 'Lead deleted successfully');
});

/**
 * @route   POST /leads/import-leads
 * @desc    Import leads from the request body and create them in the system.
 *          The user ID is extracted from the authenticated request to associate the creation with the user.
 * @access  Private (requires authentication)
 * @param   {Object} req - The request object containing the list of leads to import.
 * @param   {Object} res - The response object to send the result back.
 * @returns {Object} - A response message indicating the success or failure of the import operation.
 *
 * @throws  401 Unauthorized if userId is not found in the authenticated request.
 */
const importLeads = catchAsync(async (req, res) => {
	// Extract the userId from the authenticated request. This assumes the user is authenticated and the user ID is available.
	const userId = req.user?.userId;

	// If userId is not found in the request (i.e., user is not authenticated), respond with a 401 Unauthorized status.
	if (!userId) {
		return res.status(401).json({
			status: false,
			message: 'Unauthorized: User not found.', // Inform the client that the user is not authenticated.
		});
	}

	// Destructure the leads from the request body.
	const { leads } = req.body;

	// Process all leads in parallel to speed up the importing process using Promise.all.
	// Each lead will be processed by the importLeadsByEmail function, and it will await completion for each lead.
	await Promise.all(
		leads.map(async (lead) => {
			// Import each lead by email using the leadsService's importLeadsByEmail method.
			// Pass the lead data and the userId of the person importing the leads.
			await leadsServices.importLeadsByEmail(lead, userId);
		})
	);

	// Return a successful response indicating the file was imported successfully.
	return new ApiSuccess(res, httpStatus.OK, 'File imported successfully');
});

/**
 * @desc Export lead data to Excel
 * @route POST /api/leads/export
 *
 * This function handles the export of lead data based on the provided `leadIds` or all leads if no IDs are provided.
 * The data is exported to an Excel file and returned as a downloadable stream.
 */
const exportLeads = catchAsync(async (req, res) => {
	// Define the role (in this case, role ID is 4)
	const role = 4;

	// Extract leadIds from the request body
	const { leadIds } = req.body;

	let data;

	// Check if specific lead IDs are provided for export
	if (leadIds && leadIds.length > 0) {
		// Fetch the leads by the provided IDs for the specified role
		data = await leadsServices.getLeadsByIds(leadIds, role);
	} else {
		// If no specific IDs, fetch leads by role
		data = await leadsServices.getUserByRole(role);
	}

	// Check if any data is available for export
	if (data.length === 0) {
		throw new ApiError(
			httpStatus.BAD_REQUEST,
			'No lead data available to export'
		);
	}

	// Export the lead data to download in format of Excel
	await leadsServices.exportToExcel(data, 'Leads', 'leads.xlsx', res);
});

/**
 * Controller to fetch the lead status history for a given lead ID.
 *
 * This endpoint supports optional query parameters such as pagination, sorting, and search.
 * It delegates the business logic to the service layer and returns a standardized API response.
 *
 * @route GET /api/leads/get-lead-status-history/:id
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 *
 * @returns {Object} - JSON response with lead status history and pagination metadata
 */
const getLeadStatusHistoryById = catchAsync(async (req, res) => {
	// Extract the lead ID from request parameters
	const { id } = req.params;

	// Call service layer to fetch lead status history based on query params
	const { data, pagination } = await leadsServices.getLeadStatusHistoryByLeadId(
		{
			lead_id: id,
			...req.query, // Spread additional query parameters like page, limit, search, order_by, sort
		}
	);

	// Send a standardized API success response with data and pagination info
	return new ApiSuccess(
		res,
		httpStatus.OK,
		'Leads status fetched successfully',
		data,
		{ pagination }
	);
});

/**
 * Controller to fetch all leads lost status with optional filters, pagination, and sorting
 *
 * @route GET /get-all-leads
 * @access Private (requires authentication)
 */
const getLostStatus = catchAsync(async (req, res) => {
	// Extract user ID from the authenticated request
	// Fetch leads data using query parameters (e.g., search, pagination, filters)
	const { data, pagination } = await leadsServices.getAllLostStatus(req.query);

	// Return success response with fetched lead data
	return new ApiSuccess(
		res,
		httpStatus.OK,
		'Lost status fetched successfully',
		data,
		{ pagination }
	);
});

/**
 * Controller to update lead lost status
 *
 * @route PUT /update-lost-status/:id
 * @access Private (requires authentication)
 */
const updateLostStatus = catchAsync(async (req, res) => {
	// Extract user ID from the authenticated request
	const userId = req.user?.userId;
	// Extract lead ID from request parameters
	const { id } = req.params;

	// Call the service to update the lead with the new data
	await leadsServices.updateLeadLostStatus(id, req.body, userId);

	// Return a success response with the updated lead data
	return new ApiSuccess(
		res,
		httpStatus.OK,
		'Lost status updated successfully.'
	);
});

module.exports = {
	addLeads,
	updateAllLeads,
	getAllLeads,
	getLeadDetailsById,
	deleteLeadById,
	importLeads,
	exportLeads,
	getLeadStatusHistoryById,
	getLostStatus,
	updateLostStatus,
};
