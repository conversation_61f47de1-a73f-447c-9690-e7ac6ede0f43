const httpStatus = require('http-status');
const slugify = require('slugify');
const ApiError = require('../utils/ApiError');
const ApiSuccess = require('../utils/ApiSuccess');
const catchAsync = require('../utils/catchAsync');
const RESPONSE = require('../utils/ApiResponseMsg');
const { uploadDirectFileToS3 } = require('../utils/awsFileUpload');
const { commonService } = require('../services');

const uploadFile = catchAsync(async (req, res) => {
	const { userId } = req.user;
	const { file_name } = req.body;
	const { file } = req;

	if (!file) {
		throw new ApiError(httpStatus.BAD_REQUEST, RESPONSE.REQUIRED('file'));
	}

	const slugifiedFileName = slugify(file_name, {
		lower: true,
		strict: true, // removes special characters
	});

	const checkFile = await commonService.getFileByName(slugifiedFileName);

	if (checkFile) {
		throw new ApiError(httpStatus.BAD_REQUEST, RESPONSE.ALREADY_EXISTS('file'));
	}

	const { fileKey, fileUrl } = await uploadDirectFileToS3(
		file.buffer,
		file.originalname,
		file.mimetype,
		undefined,
		slugifiedFileName
	);

	const response = {
		fileUrl,
	};

	const fileBody = {
		file_name: slugifiedFileName,
		file_key: fileKey,
		file_url: fileUrl,
		created_by: userId,
	};

	await commonService.createFile(fileBody);

	return new ApiSuccess(
		res,
		httpStatus.OK,
		RESPONSE.UPLOAD_SUCCESS('file'),
		response
	);
});

const getAllFiles = catchAsync(async (req, res) => {
	const files = await commonService.getAllFiles();

	return new ApiSuccess(
		res,
		httpStatus.OK,
		RESPONSE.FETCH_SUCCESS('files'),
		files
	);
});

const getFilesByName = catchAsync(async (req, res) => {
	const file = await commonService.getFileByName(req.params.fileName);

	if (!file) {
		throw new ApiError(httpStatus.BAD_REQUEST, RESPONSE.NOT_FOUND('file'));
	}

	return new ApiSuccess(
		res,
		httpStatus.OK,
		RESPONSE.FETCH_SUCCESS('file'),
		file
	);
});

const deleteFileByName = catchAsync(async (req, res) => {
	const { fileName } = req.params;
	const file = await commonService.getFileByName(fileName);

	if (!file) {
		throw new ApiError(httpStatus.BAD_REQUEST, RESPONSE.NOT_FOUND('file'));
	}

	await commonService.deleteFileByName(fileName, file.file_key);

	return new ApiSuccess(res, httpStatus.OK, RESPONSE.DELETE_SUCCESS('files'));
});

module.exports = {
	uploadFile,
	getAllFiles,
	getFilesByName,
	deleteFileByName,
};
