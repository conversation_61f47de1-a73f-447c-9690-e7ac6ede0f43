module.exports = {
	async up(queryInterface, Sequelize) {
		const tableDescription = await queryInterface.describeTable('tbl_users');
		if (!tableDescription.login_pin) {
			await queryInterface.addColumn('tbl_users', 'login_pin', {
				type: Sequelize.STRING(60), // Length of 60 to store bcrypt hash
				allowNull: true,
				after: 'password', // Add column after password column
			});

			// Add unique index for login_pin
			await queryInterface.addIndex('tbl_users', ['login_pin'], {
				name: 'tbl_users_login_pin_unique',
				unique: true,
				where: {
					login_pin: {
						[Sequelize.Op.ne]: null,
					},
				},
			});
		}
	},

	async down(queryInterface) {
		const tableDescription = await queryInterface.describeTable('tbl_users');
		if (!tableDescription.login_pin) {
			await queryInterface.removeIndex(
				'tbl_users',
				'tbl_users_login_pin_unique'
			);
			await queryInterface.removeColumn('tbl_users', 'login_pin');
		}
	},
};
