const nodemailer = require('nodemailer');
const path = require('path');
const fs = require('fs').promises;
const httpStatus = require('http-status');
const config = require('../config/config');
const logger = require('../config/logger');
const ApiError = require('../utils/ApiError');

const transport = nodemailer.createTransport(config.email.smtp);

if (config.env !== 'test') {
	transport
		.verify()
		.then(() => logger.info('Connected to email server'))
		.catch(() =>
			logger.warn(
				'Unable to connect to email server. Make sure you have configured the SMTP options in .env'
			)
		);
}

/**
 * Reads an email template and replaces placeholders with provided data.
 * @param {string} templateName - Name of the email template file (without extension)
 * @param {object} replacements - Key-value pairs to replace in the template
 * @returns {Promise<string>}
 */
const getEmailTemplate = async (templateName, replacements = {}) => {
	try {
		const templatePath = path.join(
			__dirname,
			'../templates',
			`${templateName}.html`
		);
		let template = await fs.readFile(templatePath, 'utf8');

		// Replace placeholders in the template
		Object.entries(replacements).forEach(([key, value]) => {
			const placeholder = new RegExp(`{{${key}}}`, 'g');
			template = template.replace(placeholder, value);
		});

		return template;
	} catch (error) {
		logger.error(`Error loading email template: ${templateName}`, error);
		throw new Error('Email template not found');
	}
};

/**
 * Sends an email with optional attachments and inline images.
 * @param {string} to - Recipient email
 * @param {string} subject - Email subject
 * @param {string} templateName - Template file name (without extension)
 * @param {object} replacements - Key-value pairs for template placeholders
 * @returns {Promise<void>}
 */
const sendEmail = async (to, subject, templateName, replacements = {}) => {
	try {
		const htmlContent = await getEmailTemplate(templateName, replacements);

		const msg = {
			from: config.email.from,
			to,
			subject,
			html: htmlContent,
			attachments: [
				{
					filename: 'logo.png',
					path: path.join(__dirname, '../templates/images/logo.png'),
					cid: 'logo_cid', // Use this CID in the email template as <img src="cid:logo_cid">
				},
				{
					filename: 'apple.png',
					path: path.join(__dirname, '../templates/images/apple.png'),
					cid: 'apple_cid', // Use this CID in the email template as <img src="cid:logo_cid">
				},
				{
					filename: 'bg.png',
					path: path.join(__dirname, '../templates/images/bg.png'),
					cid: 'bg_cid', // Use this CID in the email template as <img src="cid:logo_cid">
				},
				{
					filename: 'facebook.png',
					path: path.join(__dirname, '../templates/images/facebook.png'),
					cid: 'facebook_cid', // Use this CID in the email template as <img src="cid:logo_cid">
				},
				{
					filename: 'instagram.png',
					path: path.join(__dirname, '../templates/images/instagram.png'),
					cid: 'instagram_cid', // Use this CID in the email template as <img src="cid:logo_cid">
				},
				{
					filename: 'lock.png',
					path: path.join(__dirname, '../templates/images/lock.png'),
					cid: 'lock_cid', // Use this CID in the email template as <img src="cid:logo_cid">
				},
				{
					filename: 'playstore.png',
					path: path.join(__dirname, '../templates/images/playstore.png'),
					cid: 'playstore_cid', // Use this CID in the email template as <img src="cid:logo_cid">
				},
				{
					filename: 'twitter.png',
					path: path.join(__dirname, '../templates/images/twitter.png'),
					cid: 'twitter_cid', // Use this CID in the email template as <img src="cid:logo_cid">
				},
				{
					filename: 'youtube.png',
					path: path.join(__dirname, '../templates/images/youtube.png'),
					cid: 'youtube_cid', // Use this CID in the email template as <img src="cid:logo_cid">
				},
			],
		};

		await transport.sendMail(msg);
		logger.info(`Email sent to ${to} with subject: ${subject}`);
	} catch (error) {
		logger.error(`Failed to send email to ${to}:`, error);
	}
};
/**
 * Sends an OTP email.
 * @param {string} to - Recipient email
 * @param {string} otp - OTP code to send
 * @returns {Promise<void>}
 */
const sendOtpEmail = async (to, OTP, USERNAME) => {
	const subject = 'OTP for Login';
	const replacements = {
		OTP,
		USERNAME,
		// appName: 'WINCO',
	};

	await sendEmail(to, subject, 'otpTemplate', replacements);
};

/**
 * Reads an email template and replaces placeholders with provided data.
 * @param {string} templateName - Name of the email template file (without extension)
 * @param {object} replacements - Key-value pairs to replace in the template
 * @returns {Promise<string>}
 */
const getLoginCredentialEmailTemplate = async (templateName, replacements) => {
	// Read the HTML template from file
	const templatePath = path.join(
		__dirname,
		'../templates',
		`${templateName}.html`
	);
	let template = await fs.readFile(templatePath, 'utf8');
	// Replace placeholders in the template

	Object.entries(replacements).forEach(([key, value]) => {
		const placeholder = new RegExp(`{{${key}}}`, 'g');
		template = template.replace(placeholder, value);
	});

	return template;
};

/**
 * Send an email
 * @param {string} to
 * @param {string} subject
 * @param {string} text
 * @returns {Promise}
 */
const sendResetEmail = async (to, subject, templateName, replacements) => {
	try {
		const htmlContent = await getLoginCredentialEmailTemplate(
			templateName,
			replacements
		);
		// Read all image files from the images directory and create attachment objects\
		const imagesDir = path.join(__dirname, '../templates/images');
		const files = await fs.readdir(imagesDir);

		const attachments = files.map((file) => ({
			filename: file,
			path: path.join(imagesDir, file),
			cid: `${file.split('.')[0]}_cid`, // CID derived from the file name
		}));

		const msg = {
			from: config.email.from,
			to,
			subject,
			html: htmlContent,
			attachments,
		};

		await transport.sendMail(msg);
	} catch (error) {
		throw new ApiError(
			httpStatus.BAD_REQUEST,
			`Failed to send email :  ${error}`
		);
	}
};

/**
 * Send reset password email
 * @param {string} to
 * @param {string} token
 * @returns {Promise}
 */
const sendResetPasswordEmail = async (to, token) => {
	const subject = 'Reset password';
	// replace this url with the link to the reset password page of your front-end app
	const resetPasswordUrl = `http://localhost:3000/api/auth/reset-password?token=${token}`;
	const text = `Dear user,
    To reset your password, click on this link: ${resetPasswordUrl}
    If you did not request any password resets, then ignore this email. Your token will be expired in 24 hours.`;
	await sendEmail(to, subject, text);
};

/**
 * Sends an email with optional attachments and inline images.
 * @param {string} to - Recipient email
 * @param {string} subject - Email subject
 * @param {string} templateName - Template file name (without extension)
 * @param {object} replacements - Key-value pairs for template placeholders
 * @returns {Promise<void>}
 */

const sendLoginCredentialEmail = async (
	to,
	subject,
	templateName,
	replacements
) => {
	try {
		const htmlContent = await getLoginCredentialEmailTemplate(
			templateName,
			replacements
		);

		// Read all image files from the images directory and create attachment objects\
		const imagesDir = path.join(__dirname, '../templates/images');
		const files = await fs.readdir(imagesDir);

		const attachments = files.map((file) => ({
			filename: file,
			path: path.join(imagesDir, file),
			cid: `${file.split('.')[0]}_cid`, // CID derived from the file name
		}));

		const msg = {
			from: config.email.from,
			to,
			subject,
			html: htmlContent,
			attachments,
		};

		await transport.sendMail(msg);
	} catch (error) {
		throw new ApiError(
			httpStatus.BAD_REQUEST,
			`Failed to send email :  ${error}`
		);
	}
};

/**
 * Sends an activation email with optional attachments and inline images.
 * @param {string} to - Recipient email
 * @param {string} activationLink - Account activation link
 * @param {string} username - User's full name
 * @param {string} host - The host URL for the application
 * @returns {Promise<void>}
 */
const sendActivationEmail = async (to, activationLink, username, host) => {
	try {
		const subject = 'Account Activation';
		const resendActivationLink = `${host}/api/auth/resend-activation?email=${encodeURIComponent(
			to
		)}`;

		const replacements = {
			USERNAME: username,
			ACTIVATION_LINK: activationLink,
			RESEND_ACTIVATION_LINK: resendActivationLink,
		};

		const htmlContent = await getLoginCredentialEmailTemplate(
			'activation-template',
			replacements
		);

		// Read all image files from the images directory and create attachment objects
		const imagesDir = path.join(__dirname, '../templates/images');
		const files = await fs.readdir(imagesDir);

		const attachments = files.map((file) => ({
			filename: file,
			path: path.join(imagesDir, file),
			cid: `${file.split('.')[0]}_cid`, // CID derived from the file name
		}));

		const msg = {
			from: config.email.from,
			to,
			subject,
			html: htmlContent,
			attachments,
		};

		await transport.sendMail(msg);
	} catch (error) {
		throw new ApiError(
			httpStatus.BAD_REQUEST,
			`Failed to send activation email: ${error}`
		);
	}
};

module.exports = {
	transport,
	sendEmail,
	sendOtpEmail,
	sendResetEmail,
	sendResetPasswordEmail,
	sendLoginCredentialEmail,
	sendActivationEmail,
};
