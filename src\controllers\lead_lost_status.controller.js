const httpStatus = require('http-status');
const leadLostStatusService = require('../services/lead_lost_status.service');
const catchAsync = require('../utils/catchAsync');
const ApiSuccess = require('../utils/ApiSuccess');
const ApiError = require('../utils/ApiError');

// Add a new lost status
const addLeadLostStatus = catchAsync(async (req, res) => {
	const userId = req.user?.userId;
	const { title, is_status } = req.body;

	const newLostStatus = await leadLostStatusService.createLeadLostStatus(
		{ title, is_status },
		userId
	);

	return new ApiSuccess(
		res,
		httpStatus.OK,
		'Lead lost status created successfully.',
		newLostStatus
	);
});

/**
 * @desc    Update an existing lead lost status
 * @route   PATCH /update-lead-lost-status/:id
 * @access  Protected
 * @param   {Object} req - Express request object with ID in params and updated fields in body
 * @param   {Object} res - Express response object
 * @returns {Object} - Success response with updated lead lost status
 */
const updateLeadLostStatus = catchAsync(async (req, res) => {
	const userId = req.user?.userId;
	const { id } = req.params;
	const { title, is_status } = req.body;
	const updatedLeadLostStatus =
		await leadLostStatusService.updateAllLeadLostStatus(
			id,
			{ title, is_status },
			userId
		);
	return new ApiSuccess(
		res,
		httpStatus.OK,
		'Lead lost status sorting updated successfully.',
		updatedLeadLostStatus
	);
});

/**
 * @desc    Update sorting order for multiple lead lost statuses
 * @route   PATCH /update-sort
 * @access  Protected
 * @param   {Object} req - Express request object with array of sort order objects
 * @param   {Object} res - Express response object
 * @returns {Object} - Success response with updated sort data
 */
const updateSortLeadLostStatus = catchAsync(async (req, res) => {
	const userId = req.user?.userId;
	const leadLostStatusSortData = req.body;

	if (
		!Array.isArray(leadLostStatusSortData) ||
		leadLostStatusSortData.length === 0
	) {
		throw new ApiError(
			httpStatus.BAD_REQUEST,
			'Invalid request: Provide an array of ID and sort values.'
		);
	}

	const updatedSortData = await leadLostStatusService.updateAllSortLostStatus(
		leadLostStatusSortData,
		userId
	);
	return new ApiSuccess(
		res,
		httpStatus.OK,
		'Lead lost status sorting updated successfully.',
		updatedSortData
	);
});

/**
 * @desc    Retrieve all lead lost statuses with pagination
 * @route   GET /get-leads-lost-status
 * @access  Protected
 * @param   {Object} req - Express request object with query parameters
 * @param   {Object} res - Express response object
 * @returns {Object} - Success response with lead lost status data and pagination
 */
const getAllLeadLostStatus = catchAsync(async (req, res) => {
	const { data, pagination } = await leadLostStatusService.getLeadAllLostStatus(
		req.query
	);
	return new ApiSuccess(
		res,
		httpStatus.OK,
		'Lead Lost status fetched successfully',
		data,
		{ pagination }
	);
});

// Delete a lead lost status
const deleteLeadLostStatus = catchAsync(async (req, res) => {
	const userId = req.user?.userId;

	const { id } = req.params;
	await leadLostStatusService.deleteAllLeadLostStatus(id, userId);
	return new ApiSuccess(
		res,
		httpStatus.OK,
		'Lead lost status deleted successfully.'
	);
});

module.exports = {
	addLeadLostStatus,
	updateLeadLostStatus,
	updateSortLeadLostStatus,
	getAllLeadLostStatus,
	deleteLeadLostStatus,
};
