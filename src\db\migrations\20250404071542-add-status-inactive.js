/** @type {import('sequelize-cli').Migration} */
module.exports = {
	up: async (queryInterface, Sequelize) => {
		const tableDescription = await queryInterface.describeTable(
			'tbl_lead_status'
		);
		if (!tableDescription.is_status) {
			await queryInterface.addColumn('tbl_lead_status', 'is_status', {
				type: Sequelize.BOOLEAN,
				allowNull: false,
				defaultValue: false, // 0 means not deleted
				comment: 'true = deleted, false = not deleted', // Add comment
			});
		}
		const tableDescription1 = await queryInterface.describeTable(
			'tbl_lead_source'
		);
		if (!tableDescription1.is_status) {
			await queryInterface.addColumn('tbl_lead_source', 'is_status', {
				type: Sequelize.BOOLEAN,
				allowNull: false,
				defaultValue: false, // 0 means not deleted
				comment: 'true = deleted, false = not deleted', // Add comment
			});
		}
		const tableDescription2 = await queryInterface.describeTable(
			'tbl_lead_types'
		);
		if (!tableDescription2.is_status) {
			await queryInterface.addColumn('tbl_lead_types', 'is_status', {
				type: Sequelize.BOOLEAN,
				allowNull: false,
				defaultValue: false, // 0 means not deleted
				comment: 'true = deleted, false = not deleted', // Add comment
			});
		}
	},

	down: async (queryInterface) => {
		const tableDescription = await queryInterface.describeTable(
			'tbl_lead_status'
		);
		if (tableDescription.is_status) {
			await queryInterface.removeColumn('tbl_lead_status', 'is_status');
		}
		const tableDescription1 = await queryInterface.describeTable(
			'tbl_lead_source'
		);
		if (tableDescription1.is_status) {
			await queryInterface.removeColumn('tbl_lead_source', 'is_status');
		}
		const tableDescription2 = await queryInterface.describeTable(
			'tbl_lead_types'
		);
		if (tableDescription2.is_status) {
			await queryInterface.removeColumn('tbl_lead_types', 'is_status');
		}
	},
};
