const Joi = require('@hapi/joi');

const leads = {
	body: Joi.object()
		.keys({
			lead_name: Joi.string().required(),
			lead_status_id: Joi.number().integer().required().messages({
				'number.base': 'Lead Status must be a number',
				'number.integer': 'Lead Status must be an integer',
				'any.required': 'Lead Status is required',
			}),
			// When lead_status_id is 6 (Lost), lost_status_id and lost_status_comments are required
			lost_status_id: Joi.alternatives().conditional('lead_status_id', {
				is: 6,
				then: Joi.number().required().messages({
					'any.required': 'Lost Status is required when Lead Status is Lost.',
				}),
				otherwise: Joi.any().strip(),
			}),

			lost_status_comments: Joi.alternatives().conditional('lead_status_id', {
				is: 6,
				then: Joi.string().required().messages({
					'any.required': 'Comments are required when Lead Status is Lost.',
				}),
				otherwise: Joi.any().strip(),
			}),
			// If lead_status_id is 1, forbid lost_status_id and lost_status_comments
			lead_types_id: Joi.number().integer().required().messages({
				'number.base': 'Lead Types must be a number',
				'number.integer': 'Lead Types must be an integer',
				'any.required': 'Lead Types is required',
			}),
			lead_source_id: Joi.number()
				.allow('', null)
				.optional()
				.integer()
				.messages({
					'number.base': 'Lead source must be a number',
					'number.integer': 'Lead source must be an integer',
					'any.required': 'Lead source is required',
				}),
			sales_rep: Joi.number().integer().required().messages({
				'number.base': 'Sales rep must be a number',
				'number.integer': 'Sales rep must be an integer',
				'any.required': 'Sales rep is required',
			}),
			company_name: Joi.string().allow('', null).optional().max(255),
			company_email: Joi.string().allow('', null).optional().max(255),
			company_phone: Joi.string()
				.pattern(/^[0-9]+$/)
				.allow('', null)
				.optional()
				.messages({
					'string.pattern.base':
						'Company phone number must contain only digits',
				}),
			website: Joi.string().allow('', null).optional().uri().max(255).messages({
				'string.uri': 'Website must be a valid URL',
				'string.max': 'Website must not exceed 255 characters',
			}),
			address: Joi.string().allow('', null).optional().max(500).messages({
				'string.max': 'Address must not exceed 500 characters',
			}),
			address2: Joi.string().allow('', null).optional().max(500).messages({
				'string.max': 'Address2 must not exceed 500 characters',
			}),
			city: Joi.string().allow('', null).optional().max(100).messages({
				'string.max': 'City must not exceed 100 characters',
			}),
			state: Joi.string().allow('', null).optional().max(100).messages({
				'string.max': 'State must not exceed 100 characters',
			}),
			zip: Joi.string()
				.allow('', null)
				.pattern(/^[0-9]{5}(-[0-9]{4})?$/) // US ZIP code format
				.optional()
				.messages({
					'string.pattern.base':
						'Zip code must be in the format 12345 or 12345-6789',
				}),
			country: Joi.string().allow('', null).optional().max(100).messages({
				'string.max': 'Country must not exceed 100 characters',
			}),
			notes: Joi.string().optional().allow(null, ''), // text field
			first_name: Joi.string().required(),
			last_name: Joi.string().allow('', null).optional().max(255),
			primary_email: Joi.string().email().allow('', null).optional(),
			mobile_no: Joi.string()
				.pattern(/^[0-9]{10,15}$/)
				.allow('', null)
				.optional()
				.messages({
					'string.pattern.base': 'Mobile number must be 10 to 15 digits',
				}),
			job_title: Joi.string().allow('', null).optional().max(255),
			facebook: Joi.string()
				.allow('', null)
				.optional()
				.uri()
				.max(255)
				.messages({
					'string.uri': 'Facebook must be a valid URL',
					'string.max': 'Facebook url must not exceed 255 characters',
				}),
			twitter: Joi.string().allow('', null).optional().uri().max(255).messages({
				'string.uri': 'Twitter must be a valid URL',
				'string.max': 'Twitter url must not exceed 255 characters',
			}),
			linkedin: Joi.string()
				.allow('', null)
				.optional()
				.uri()
				.max(255)
				.messages({
					'string.uri': 'Linkedin must be a valid URL',
					'string.max': 'Linkedin url must not exceed 255 characters',
				}),
			instagram: Joi.string()
				.allow('', null)
				.optional()
				.uri()
				.max(255)
				.messages({
					'string.uri': 'Instagram must be a valid URL',
					'string.max': 'Instagram url must not exceed 255 characters',
				}),
			youtube: Joi.string().allow('', null).optional().uri().max(255).messages({
				'string.uri': 'Youtube must be a valid URL',
				'string.max': 'Youtube url must not exceed 255 characters',
			}),
			pinterest: Joi.string()
				.allow('', null)
				.optional()
				.uri()
				.max(255)
				.messages({
					'string.uri': 'Pinterest must be a valid URL',
					'string.max': 'Pinterest url must not exceed 255 characters',
				}),
			whatsapp: Joi.string()
				.allow('', null)
				.optional()
				.uri()
				.max(255)
				.messages({
					'string.uri': 'Whatsapp must be a valid URL',
					'string.max': 'Whatsapp url must not exceed 255 characters',
				}),
			tiktok: Joi.string().allow('', null).optional().uri().max(255).messages({
				'string.uri': 'Tiktok must be a valid URL',
				'string.max': 'Tiktok url must not exceed 255 characters',
			}),
		})
		.custom((value, helpers) => {
			const email = value.primary_email?.trim();
			const mobile = value.mobile_no?.trim();

			const isEmailValid =
				email && Joi.string().email().validate(email).error === undefined;
			const isMobileValid = mobile && /^[0-9]{10,15}$/.test(mobile);

			if (!isEmailValid && !isMobileValid) {
				return helpers.message(
					'At least one of primary_email or mobile_no must be a valid non-empty value'
				);
			}

			return value;
		}), // Ensures at least one field is present
};

const importLeadsFile = Joi.object({
	leads: Joi.array()
		.items(
			Joi.object({
				lead_name: Joi.string().required(),
				lead_status_id: Joi.number().integer().required().messages({
					'number.base': 'Lead Status must be a number',
					'number.integer': 'Lead Status must be an integer',
					'any.required': 'Lead Status is required',
				}),
				lost_status_id: Joi.when('lead_status_id', {
					is: 6,
					then: Joi.number().integer().required().messages({
						'any.required': 'Lost Status is required when Lead Status is Lost',
						'number.base': 'Lost Status must be a number',
						'number.integer': 'Lost Status must be an integer',
					}),
					otherwise: Joi.any().optional(),
				}),
				comments: Joi.when('lead_status_id', {
					is: 6,
					then: Joi.string().trim().required().messages({
						'any.required': 'Comments are required when Lead Status is Lost',
						'string.base': 'Comments must be text',
					}),
					otherwise: Joi.any().optional(),
				}),
				lead_types_id: Joi.number().integer().required().messages({
					'number.base': 'Lead Types must be a number',
					'number.integer': 'Lead Types must be an integer',
					'any.required': 'Lead Types is required',
				}),
				lead_source_id: Joi.number()
					.allow('', null)
					.optional()
					.integer()
					.messages({
						'number.base': 'Lead Types must be a number',
						'number.integer': 'Lead Types must be an integer',
						'any.required': 'Lead Types is required',
					}),
				sales_rep: Joi.number().integer().required().messages({
					'number.base': 'Sales rep must be a number',
					'number.integer': 'Sales rep must be an integer',
					'any.required': 'Sales rep is required',
				}),
				company_name: Joi.string().allow('', null).optional().max(255),
				company_email: Joi.string().allow('', null).optional().max(255),
				company_phone: Joi.string()
					.pattern(/^[0-9]+$/)
					.allow('', null)
					.optional()
					.messages({
						'string.pattern.base':
							'Company phone number must contain only digits',
					}),
				website: Joi.string()
					.allow('', null)
					.optional()
					.uri()
					.max(255)
					.messages({
						'string.uri': 'Website must be a valid URL',
						'string.max': 'Website must not exceed 255 characters',
					}),
				address: Joi.string().allow('', null).optional().max(500).messages({
					'string.max': 'Address must not exceed 500 characters',
				}),
				address2: Joi.string().allow('', null).optional().max(500).messages({
					'string.max': 'Address2 must not exceed 500 characters',
				}),
				city: Joi.string().allow('', null).optional().max(100).messages({
					'string.max': 'City must not exceed 100 characters',
				}),
				state: Joi.string().allow('', null).optional().max(100).messages({
					'string.max': 'State must not exceed 100 characters',
				}),
				zip: Joi.string()
					.allow('', null)
					.pattern(/^[0-9]{5}(-[0-9]{4})?$/) // US ZIP code format
					.optional()
					.messages({
						'string.pattern.base':
							'Zip code must be in the format 12345 or 12345-6789',
					}),
				country: Joi.string().allow('', null).optional().max(100).messages({
					'string.max': 'Country must not exceed 100 characters',
				}),
				notes: Joi.string().optional().allow(null, ''), // text field
				first_name: Joi.string().required(),
				last_name: Joi.string().allow('', null).optional().max(255),
				primary_email: Joi.string().email().allow('', null).optional(),
				mobile_no: Joi.string()
					.pattern(/^[0-9]{10,15}$/)
					.allow('', null)
					.optional()
					.messages({
						'string.pattern.base': 'Mobile number must be 10 to 15 digits',
					}),
				job_title: Joi.string().allow('', null).optional().max(255),
				facebook: Joi.string()
					.allow('', null)
					.optional()
					.uri()
					.max(255)
					.messages({
						'string.uri': 'Facebook must be a valid URL',
						'string.max': 'Facebook url must not exceed 255 characters',
					}),
				twitter: Joi.string()
					.allow('', null)
					.optional()
					.uri()
					.max(255)
					.messages({
						'string.uri': 'Twitter must be a valid URL',
						'string.max': 'Twitter url must not exceed 255 characters',
					}),
				linkedin: Joi.string()
					.allow('', null)
					.optional()
					.uri()
					.max(255)
					.messages({
						'string.uri': 'Linkedin must be a valid URL',
						'string.max': 'Linkedin url must not exceed 255 characters',
					}),
				instagram: Joi.string()
					.allow('', null)
					.optional()
					.uri()
					.max(255)
					.messages({
						'string.uri': 'Instagram must be a valid URL',
						'string.max': 'Instagram url must not exceed 255 characters',
					}),
				youtube: Joi.string()
					.allow('', null)
					.optional()
					.uri()
					.max(255)
					.messages({
						'string.uri': 'Youtube must be a valid URL',
						'string.max': 'Youtube url must not exceed 255 characters',
					}),
				pinterest: Joi.string()
					.allow('', null)
					.optional()
					.uri()
					.max(255)
					.messages({
						'string.uri': 'Pinterest must be a valid URL',
						'string.max': 'Pinterest url must not exceed 255 characters',
					}),
				whatsapp: Joi.string()
					.allow('', null)
					.optional()
					.uri()
					.max(255)
					.messages({
						'string.uri': 'Whatsapp must be a valid URL',
						'string.max': 'Whatsapp url must not exceed 255 characters',
					}),
				tiktok: Joi.string()
					.allow('', null)
					.optional()
					.uri()
					.max(255)
					.messages({
						'string.uri': 'Tiktok must be a valid URL',
						'string.max': 'Tiktok url must not exceed 255 characters',
					}),
			}).custom((value, helpers) => {
				const email = value.primary_email?.trim();
				const mobile = value.mobile_no?.trim();

				const isEmailValid =
					email && Joi.string().email().validate(email).error === undefined;
				const isMobileValid = mobile && /^[0-9]{10,15}$/.test(mobile);

				if (!isEmailValid && !isMobileValid) {
					return helpers.message(
						'At least one of primary_email or mobile_no must be a valid non-empty value'
					);
				}

				return value;
			}) // Ensures at least one field is present
		)
		.min(1)
		.required(),
});

const lostStatus = {
	body: Joi.object().keys({
		lost_status_id: Joi.number().integer().required().messages({
			'number.base': 'Lost status must be a number',
			'number.integer': 'Lost status must be an integer',
			'any.required': 'Lost status is required',
		}),
		lost_status_comments: Joi.string().trim().min(1).required().messages({
			'string.base': 'Lost status comment must be a string',
			'string.empty': 'Lost status comment is required',
			'any.required': 'Lost status comment is required',
		}),
	}),
};
module.exports = {
	leads,
	importLeadsFile,
	lostStatus,
};
