const Joi = require('@hapi/joi');
const { password } = require('./custom.validation');

const register = {
	body: Joi.object().keys({
		registrationFrom: Joi.string()
			.valid('ecom', 'erp', 'app')
			.required()
			.messages({
				'any.only': 'Registration source must be either ecom, erp, or app',
				'any.required': 'Registration source is required',
			}),
		firstName: Joi.string().required().max(255).messages({
			'string.empty': 'First name is required',
			'any.required': 'First name is required',
		}),
		lastName: Joi.string().allow('', null).optional().max(255),
		mobileNo: Joi.string()
			.pattern(/^[0-9]+$/)
			.required()
			.messages({
				'string.pattern.base': 'Mobile number must contain only digits',
				'string.empty': 'Mobile number is required',
			}),
		emailId: Joi.string()
			.required()
			.email({
				minDomainSegments: 2,
				tlds: { allow: true },
			})
			.max(255)
			.messages({
				'string.email':
					'Please provide a valid email address (e.g., <EMAIL>)',
				'string.empty': 'Email address is required',
				'any.required': 'Email address is required',
				'string.max': 'Email address must not exceed 255 characters',
				'string.base': 'Email address must be a valid string',
			}),
		password: Joi.string()
			.required()
			.min(8)
			.max(32) // Optional: Set a max length
			.regex(
				/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]+$/
			)
			.messages({
				'string.min': 'Password must be at least 8 characters long.',
				'string.max': 'Password must not exceed 32 characters.',
				'string.pattern.base':
					'Password must include at least one uppercase letter, one lowercase letter, one digit, and one special character (@$!%*?&).',
				'string.empty': 'Password is required',
				'any.required': 'Password is required',
			}),
		confirmPassword: Joi.string()
			.required()
			.valid(Joi.ref('password'))
			.messages({
				'any.only': 'Confirm password must match password',
				'string.empty': 'Confirm password is required',
				'any.required': 'Confirm password is required',
			}),
		role_id: Joi.number().valid(2, 3).required().messages({
			'any.required': 'Role ID is required.',
			'number.base':
				'Role ID must be a valid number (2 for Homeowner, 3 for Dealer).',
		}),

		companyName: Joi.when('role_id', {
			is: 3,
			then: Joi.string().allow('', null).optional().max(255).messages({
				'any.required': 'Company name is required for role_id 3',
				'string.max': 'Company name must not exceed 255 characters',
			}),
			otherwise: Joi.string().allow('', null).optional(),
		}),

		companyPhone: Joi.when('role_id', {
			is: 3,
			then: Joi.string()
				.allow('', null)
				.pattern(/^[0-9]+$/)
				.optional()
				.messages({
					'string.pattern.base': 'Company phone must contain only digits',
					'any.required': 'Company phone is required for role_id 3',
				}),
			otherwise: Joi.string().allow('', null).optional(),
		}),

		website: Joi.when('role_id', {
			is: 3,
			then: Joi.string().allow('', null).optional().uri().max(255).messages({
				'string.uri': 'Website must be a valid URL',
				'string.max': 'Website must not exceed 255 characters',
			}),
			otherwise: Joi.string().allow('', null).optional(), // Make it truly optional for other role_ids
		}),
		address: Joi.string().allow('', null).optional().max(500).messages({
			'string.max': 'Address must not exceed 500 characters',
		}),
		city: Joi.string().allow('', null).optional().max(100).messages({
			'string.max': 'City must not exceed 100 characters',
		}),
		state: Joi.string().allow('', null).optional().max(100).messages({
			'string.max': 'State must not exceed 100 characters',
		}),
		country: Joi.string().allow('', null).optional().max(100).messages({
			'string.max': 'Country must not exceed 100 characters',
		}),
		zip: Joi.string()
			.allow('', null)
			.pattern(/^[0-9]{5}(-[0-9]{4})?$/) // US ZIP code format
			.optional()
			.messages({
				'string.pattern.base':
					'Zip code must be in the format 12345 or 12345-6789',
			}),
	}),
};
const activateAccount = {
	query: Joi.object().keys({
		token: Joi.string().required().messages({
			'any.required': 'Activation token is required',
			'string.empty': 'Activation token cannot be empty',
		}),
	}),
};

const resendActivation = {
	query: Joi.object().keys({
		email: Joi.string().email().required(),
	}),
};

const login = {
	body: Joi.object()
		.keys({
			email: Joi.string().allow('', null).optional().email().messages({
				'string.email': 'Invalid email format.',
			}),

			password: Joi.string().allow('', null).optional().min(6).messages({
				'string.min': 'Password must be at least 6 characters long.',
			}),

			phoneNumber: Joi.string()
				.allow('', null)
				.optional()
				.pattern(/^\d+$/)
				.messages({
					'string.pattern.base': 'Phone number must contain only digits.',
				}),
			pin: Joi.string().allow('', null).optional().pattern(/^\d+$/).messages({
				'string.min': 'PIN must be at least 6 characters long.',
				'string.pattern.base': 'PIN must contain only digits.',
			}),
			loginMethod: Joi.string()
				.valid('password', 'email_otp', 'phone_otp', 'pin')
				.required()
				.messages({
					'any.only':
						"Invalid login method. Use 'password', 'email_otp', 'pin', or 'phone_otp'.",
					'any.required': 'Login method is required.',
				}),
			app_type: Joi.string().valid('erp', 'ecom', 'app').required().messages({
				'any.required': 'Application type is required',
				'any.only': 'Application type must be one of: erp, ecom, or app',
			}),
		})
		.custom((value, helpers) => {
			// Enforce field requirements based on loginMethod
			if (
				value.loginMethod === 'email_otp' &&
				(!value.email || value.email.trim() === '')
			) {
				return helpers.message('Email is required for email OTP login.');
			}
			if (
				value.loginMethod === 'phone_otp' &&
				(!value.phoneNumber || value.phoneNumber.trim() === '')
			) {
				return helpers.message('Phone number is required for phone OTP login.');
			}
			if (
				value.loginMethod === 'pin' &&
				(!value.pin || value.pin.trim() === '')
			) {
				return helpers.message('PIN is required for PIN login.');
			}
			if (value.loginMethod === 'password') {
				if (!value.email || value.email.trim() === '') {
					return helpers.message('Email is required for password login.');
				}
				if (!value.password || value.password.trim() === '') {
					return helpers.message('Password is required for password login.');
				}
			}
			return value; // Return validated value
		}),
};

const verifyOtp = {
	body: Joi.object().keys({
		unique_id: Joi.string().required().messages({
			'any.required': 'Unique ID is required.',
			'string.uuid': 'Unique ID must be a valid UUID.',
		}),

		otp: Joi.string().required().length(6).pattern(/^\d+$/).messages({
			'any.required': 'OTP is required.',
			'string.length': 'OTP must be exactly 6 digits.',
			'string.pattern.base': 'OTP must contain only numbers.',
		}),
	}),
};
// Validation for resending OTP
const resendOtp = {
	body: Joi.object().keys({
		unique_id: Joi.string().required(),
		type: Joi.string().valid('email_otp', 'phone_otp').required(),
	}),
};
const forgotPassword = {
	body: Joi.object().keys({
		email: Joi.string().email().required(),
		app_type: Joi.string().valid('ecom', 'erp').required(),
	}),
};

const resetPassword = {
	body: Joi.object().keys({
		token: Joi.string().required(),
		newPassword: Joi.string().required().custom(password),
		confirmPassword: Joi.string()
			.required()
			.valid(Joi.ref('newPassword'))
			.messages({
				'any.only': 'Confirm password and new password must be same.',
			}),
	}),
};

const logoutUser = {
	body: Joi.object().keys({
		email: Joi.string().email().required(),
	}),
};

// Validation for Google OAuth data
const socialAuth = {
	body: Joi.object().keys({
		data: Joi.object({
			email: Joi.string().required().email(),
			email_verified: Joi.boolean().required(),
			given_name: Joi.string().allow(null, ''),
			family_name: Joi.string().allow(null, ''),
			name: Joi.string().required(),
			sub: Joi.string().allow(null, ''),
			image_base64: Joi.string().allow(null, ''),
			social_type: Joi.string().required().valid('google', 'facebook'),
			registrationFrom: Joi.string()
				.valid('ecom', 'website', 'app')
				.required()
				.messages({
					'any.only':
						'Registration source must be either ecom, website, or app',
					'any.required': 'Registration source is required',
				}),
		}).required(),
	}),
};

module.exports = {
	register,
	activateAccount,
	resendActivation,
	login,
	forgotPassword,
	resetPassword,
	verifyOtp,
	logoutUser,
	resendOtp,
	socialAuth,
};
