/** @type {import('sequelize-cli').Migration} */
module.exports = {
	up: async (queryInterface, Sequelize) => {
		const tableDescription = await queryInterface.describeTable('tbl_users');

		if (!tableDescription.website) {
			await queryInterface.addColumn('tbl_users', 'website', {
				type: Sequelize.STRING,
				allowNull: true,
			});
		}
		if (!tableDescription.company_name) {
			await queryInterface.addColumn('tbl_users', 'company_name', {
				type: Sequelize.STRING,
				allowNull: true,
			});
		}
		if (!tableDescription.company_phone) {
			await queryInterface.addColumn('tbl_users', 'company_phone', {
				type: Sequelize.STRING,
				allowNull: true,
			});
		}
		if (!tableDescription.address) {
			await queryInterface.addColumn('tbl_users', 'address', {
				type: Sequelize.STRING,
				allowNull: true,
			});
		}
		if (!tableDescription.city) {
			await queryInterface.addColumn('tbl_users', 'city', {
				type: Sequelize.STRING,
				allowNull: true,
			});
		}
		if (!tableDescription.state) {
			await queryInterface.addColumn('tbl_users', 'state', {
				type: Sequelize.STRING,
				allowNull: true,
			});
		}
		if (!tableDescription.zip) {
			await queryInterface.addColumn('tbl_users', 'zip', {
				type: Sequelize.STRING,
				allowNull: true,
			});
		}
		if (!tableDescription.country) {
			await queryInterface.addColumn('tbl_users', 'country', {
				type: Sequelize.STRING,
				allowNull: true,
			});
		}
	},

	down: async (queryInterface) => {
		const tableDescription = await queryInterface.describeTable('tbl_users');

		if (tableDescription.website) {
			await queryInterface.removeColumn('tbl_users', 'website');
		}
		if (tableDescription.company_name) {
			await queryInterface.removeColumn('tbl_users', 'company_name');
		}
		if (tableDescription.company_phone) {
			await queryInterface.removeColumn('tbl_users', 'company_phone');
		}
		if (tableDescription.address) {
			await queryInterface.removeColumn('tbl_users', 'address');
		}
		if (tableDescription.city) {
			await queryInterface.removeColumn('tbl_users', 'city');
		}
		if (tableDescription.state) {
			await queryInterface.removeColumn('tbl_users', 'state');
		}
		if (tableDescription.zip) {
			await queryInterface.removeColumn('tbl_users', 'zip');
		}
		if (tableDescription.country) {
			await queryInterface.removeColumn('tbl_users', 'country');
		}
	},
};
