const httpStatus = require('http-status');
const bcrypt = require('bcrypt');
const ApiSuccess = require('../utils/ApiSuccess');
const catchAsync = require('../utils/catchAsync');
const ApiError = require('../utils/ApiError');
const {
	dealerService,
	bankService,
	emailService,
	leadsService,
} = require('../services');
const RESPONSE = require('../utils/ApiResponseMsg');

/**
 * @desc      Import dealers from the request body.
 * @route     POST /api/dealers/import
 * @throws    {ApiError} If an error occurs during dealer import.
 */
const importDealer = catchAsync(async (req, res) => {
	const { userId } = req.user;
	const { dealers } = req.body;

	const primaryEmails = dealers.map((dealer) => dealer.primary_email);
	const mobileNumbers = dealers.map((dealer) => dealer.mobile_no);

	// Step 2: Perform a bulk check for existing dealers
	const existingDealers = await dealerService.getExistingDealersBulk(
		primaryEmails,
		mobileNumbers
	);

	// Create maps for quick lookup
	const emailMap = new Map();
	const mobileMap = new Map();

	existingDealers.forEach((user) => {
		if (user.primary_email) emailMap.set(user.primary_email, user);
		if (user.mobile_no) mobileMap.set(user.mobile_no, user);
	});

	// Process all dealers
	await Promise.all(
		dealers.map(async (dealer) => {
			const existingUser =
				emailMap.get(dealer.primary_email) || mobileMap.get(dealer.mobile_no);

			if (existingUser?.id) {
				await dealerService.updateDealerById(existingUser.id, dealer, userId);
			} else {
				await dealerService.createDealer(dealer, userId);
			}
		})
	);

	return new ApiSuccess(res, httpStatus.OK, RESPONSE.CREATE_SUCCESS('dealer'));
});

/**
 * @desc      Export dealers to an Excel file.
 * @route     GET /api/dealers/export
 * @throws    {ApiError} If an error occurs during dealer export.
 */
const exportDealer = catchAsync(async (req, res) => {
	const { userId } = req.user;
	const role = 3;

	const { dealerIds } = req.body;

	let data;

	if (userId === 1) {
		// Export all dealers when super admin request
		data = await dealerService.getUserByRole(role);
	} else if (dealerIds && dealerIds.length > 0) {
		// Export selected dealer
		data = await dealerService.getDealersByIds(dealerIds, role);
	}

	if (data.length === 0) {
		throw new ApiError(
			httpStatus.BAD_REQUEST,
			'No dealer data available to export'
		);
	}

	// Export all dealers

	dealerService.exportToExcel(data, 'Dealers', 'dealers.xlsx', res);

	// return new ApiSuccess(res, httpStatus.OK, 'Dealer file exported successfully', data);
});

/**
 * @desc      Get all dealers with optional filters.
 * @route     GET /api/dealers
 * @throws    {ApiError} If an error occurs during fetching dealers.
 */
const getAllDealers = catchAsync(async (req, res) => {
	const role = 3;
	const { rows, pagination } = await dealerService.getAllDealers(
		role,
		req.query
	);

	return new ApiSuccess(
		res,
		httpStatus.OK,
		RESPONSE.FETCH_SUCCESS('dealer'),
		rows,
		{ pagination }
	);
});

/**
 * @desc      Get a dealer by their ID.
 * @route     GET /api/dealers/:dealerId
 * @throws    {ApiError} If the dealer is not found.
 */
const getDealerById = catchAsync(async (req, res) => {
	const dealerId = req.params.dealerId || req.user.userId;

	const dealer = await dealerService.getUserById(dealerId);

	let dealerBankDetails = null;
	const bank = await bankService.getBankByDealerId(dealer.dealer_id);

	if (bank) {
		dealerBankDetails = {
			id: bank.id,
			dealer_id: bank.dealer_id,
			account_name: bank.account_name.toString(),
			bank_name: bank.bank_name.toString(),
			account_number: bank.account_number.toString(),
			ifsc: bank.ifsc.toString(),
			branch_name: bank.branch_name.toString(),
		};
	}

	// Convert Sequelize instance to plain object
	const flattendDealer = {
		...dealer.get({ plain: true }),
		...dealer.dealer?.get({ plain: true }),
	};
	delete flattendDealer.dealer;
	flattendDealer.bank = dealerBankDetails;

	return new ApiSuccess(
		res,
		httpStatus.OK,
		RESPONSE.FETCH_SUCCESS('dealer'),
		flattendDealer
	);
});

/**
 * @desc      Create a new dealer.
 * @route     POST /api/dealers
 * @throws    {ApiError} If dealer creation fails.
 */
const createDealer = catchAsync(async (req, res) => {
	const { primary_email, first_name, last_name } = req.body;
	const { userId } = req.user;

	const randomPassword = 'winco@123';

	req.body.password = await bcrypt.hash(randomPassword, 10);

	const dealer = await dealerService.createDealer(req.body, userId);

	const fullName = `${first_name} ${last_name}`;

	const subject = 'Login Credentials';
	const replacements = {
		email: primary_email,
		password: randomPassword,
		USERNAME: fullName,
	};

	// send login details on dealer email
	await emailService.sendLoginCredentialEmail(
		primary_email,
		subject,
		'login-credential-template',
		replacements
	);

	const user = {
		first_name: dealer.first_name,
		last_name: dealer.last_name,
		id: dealer.id,
	};

	return new ApiSuccess(
		res,
		httpStatus.OK,
		RESPONSE.CREATE_SUCCESS('dealer'),
		user
	);
});

/**
 * @desc      Delete a dealer by their ID.
 * @route     DELETE /api/dealers/:dealerId
 * @throws    {ApiError} If the dealer is not found or deletion fails.
 */
const deleteDealer = catchAsync(async (req, res) => {
	await dealerService.deleteDealerById(req.params.dealerId);

	return new ApiSuccess(res, httpStatus.OK, RESPONSE.DELETE_SUCCESS('dealer'));
});

/**
 * @desc      Update dealer information by ID.
 * @route     PUT /api/dealers/:dealerId
 * @throws    {ApiError} If the dealer update fails.
 */
const updateDealer = catchAsync(async (req, res) => {
	const dealerId = req.params.dealerId || req.user.userId;
	const { userId } = req.user;

	const dealer = await dealerService.updateDealerById(
		dealerId,
		req.body,
		userId
	);

	return new ApiSuccess(
		res,
		httpStatus.OK,
		RESPONSE.UPDATE_SUCCESS('dealer'),
		dealer
	);
});

/**
 * @desc      Upload dealer files to AWS S3.
 * @route     POST /api/dealers/upload
 * @throws    {ApiError} If the file upload fails.
 */
const uploadFile = catchAsync(async (req, res) => {
	const { dealer_id, lead_id, files } = req.body;

	if ((!dealer_id && !lead_id) || !Array.isArray(files) || files.length === 0) {
		throw new ApiError(
			httpStatus.BAD_REQUEST,
			'Either dealer_id or lead_id is required, and files must be a non-empty array.'
		);
	}

	const uploadedFile = await dealerService.uploadFile(
		dealer_id,
		lead_id,
		files
	);

	const uploadedFileRes = uploadedFile.map((data) => {
		return {
			id: data.id,
			file_name: data.file_name,
			file_url: data.file_url,
			file_type: data.file_type,
			created_at: data.created_at,
		};
	});

	return new ApiSuccess(
		res,
		httpStatus.OK,
		RESPONSE.UPLOAD_SUCCESS('dealer files'),
		uploadedFileRes
	);
});

/**
 * @desc      Delete dealer files from AWS S3 and the database.
 * @route     DELETE /api/dealers/files
 * @throws    {ApiError} If the file deletion fails.
 */
const deleteFiles = catchAsync(async (req, res) => {
	const { userId } = req.user;
	const { dealer_id, lead_id, file_ids } = req.body;

	if (dealer_id) {
		await dealerService.getDealerById(dealer_id);
	}
	if (lead_id) {
		await leadsService.getLeadById(lead_id);
	}

	// const fileKeys = await dealerService.getDealerFiles(dealer_id, file_ids)

	// if (!Array.isArray(fileKeys) || fileKeys.length === 0) {
	//   throw new ApiError(httpStatus.BAD_REQUEST, "No files found for deletion");
	// }

	// try {
	//   // delete form aws
	//   await dealerService.deleteUploadedFileFromAws(fileKeys);

	// } catch (err) {
	//   throw new ApiError(httpStatus.BAD_REQUEST, `S3 file deletion failed--${err}`);
	// }

	// delete from db
	await dealerService.deleteUploadedFile(dealer_id, lead_id, file_ids, userId);

	return new ApiSuccess(
		res,
		httpStatus.OK,
		RESPONSE.DELETE_SUCCESS('dealer files')
	);
});

/**
 * @desc      Get dealer files by ID.
 * @route     GET /api/admin/files/dealer/:dealerId
 * @throws    {ApiError} If the dealer update fails.
 */
const getFilesByUserId = catchAsync(async (req, res) => {
	const { dealerId, leadId } = req.params;

	if (dealerId) {
		await dealerService.getDealerById(dealerId);
	} else {
		await leadsService.getLeadById(leadId);
	}

	const { rows, pagination } = await dealerService.getfilesByUserId(
		dealerId,
		leadId,
		req.query
	);

	return new ApiSuccess(
		res,
		httpStatus.OK,
		RESPONSE.FETCH_SUCCESS('files'),
		rows,
		{ pagination }
	);
});

module.exports = {
	importDealer,
	exportDealer,
	getAllDealers,
	getDealerById,
	createDealer,
	deleteDealer,
	updateDealer,
	uploadFile,
	deleteFiles,
	getFilesByUserId,
};
