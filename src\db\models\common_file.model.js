module.exports = (sequelize, DataTypes) => {
	const CommonFile = sequelize.define(
		'tbl_common_file',
		{
			id: {
				allowNull: false,
				autoIncrement: true,
				primaryKey: true,
				type: DataTypes.INTEGER,
			},
			file_name: {
				type: DataTypes.STRING,
				allowNull: false,
				unique: true,
			},
			file_key: {
				type: DataTypes.STRING,
				allowNull: false,
			},
			file_url: {
				type: DataTypes.STRING,
				allowNull: false,
			},
			created_by: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'tbl_wo_roles',
					key: 'id',
				},
				onDelete: 'CASCADE',
				onUpdate: 'CASCADE',
			},
			created_at: {
				allowNull: false,
				type: DataTypes.DATE,
			},
			updated_at: {
				allowNull: true,
				type: DataTypes.DATE,
				defaultValue: null,
			},
		},
		{
			sequelize,
			tableName: 'tbl_common_file',
			timestamps: true,
			// paranoid: true, // soft delete
			createdAt: 'created_at',
			updatedAt: 'updated_at',
		}
	);

	return CommonFile;
};
