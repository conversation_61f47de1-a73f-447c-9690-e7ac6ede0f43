/** @type {import('sequelize-cli').Migration} */
// c:\Users\<USER>\Desktop\your_project\migrations\20231010_create_tbl_dealer_status_log.js

module.exports = {
	up: (queryInterface, Sequelize) =>
		queryInterface.createTable('tbl_dealer_status_log', {
			id: {
				type: Sequelize.INTEGER,
				allowNull: false,
				primaryKey: true,
				autoIncrement: true,
			},
			dealer_id: {
				type: Sequelize.INTEGER,
				allowNull: false,
			},
			status: {
				type: Sequelize.INTEGER,
				allowNull: false,
			},
			approved_by: {
				type: Sequelize.INTEGER,
				allowNull: true,
			},
			approved_date: {
				type: Sequelize.DATE,
				allowNull: true,
			},
			reason: {
				type: Sequelize.TEXT,
				allowNull: true,
			},
			rejected_by: {
				type: Sequelize.INTEGER,
				allowNull: true,
			},
			rejected_date: {
				type: Sequelize.DATE,
				allowNull: true,
			},
			created_by: {
				type: Sequelize.INTEGER,
				allowNull: false,
			},
			created_date: {
				type: Sequelize.DATE,
				defaultValue: Sequelize.NOW,
				allowNull: false,
			},
		}),
	down: (queryInterface /* , Sequelize */) =>
		queryInterface.dropTable('tbl_dealer_status_log'),
};
