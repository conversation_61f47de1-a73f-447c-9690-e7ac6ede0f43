/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up(queryInterface, Sequelize) {
		const tableDescription = await queryInterface.describeTable(
			'tbl_dealer_files'
		);

		if (!tableDescription.deleted_at) {
			await queryInterface.addColumn('tbl_dealer_files', 'deleted_at', {
				type: Sequelize.DATE,
				allowNull: true,
			});
		}

		if (!tableDescription.deleted_by) {
			await queryInterface.addColumn('tbl_dealer_files', 'deleted_by', {
				type: Sequelize.INTEGER,
				allowNull: true,
			});
		}

		if (!tableDescription.is_deleted) {
			await queryInterface.addColumn('tbl_dealer_files', 'is_deleted', {
				type: Sequelize.BOOLEAN,
				defaultValue: false,
				allowNull: false,
			});
		}
	},

	async down(queryInterface) {
		const tableDescription = await queryInterface.describeTable(
			'tbl_dealer_files'
		);

		if (tableDescription.deleted_at) {
			await queryInterface.removeColumn('tbl_dealer_files', 'deleted_at');
		}

		if (tableDescription.deleted_by) {
			await queryInterface.removeColumn('tbl_dealer_files', 'deleted_by');
		}

		if (tableDescription.is_deleted) {
			await queryInterface.removeColumn('tbl_dealer_files', 'is_deleted');
		}
	},
};
