const db = require('../db/models');
const { deleteFromS3 } = require('../utils/awsFileUpload');

const createFile = async (fileBody) => {
	await db.tbl_common_file.create(fileBody);

	return true;
};

const getFileByName = async (file_name) => {
	const file = await db.tbl_common_file.findOne({
		where: { file_name },
		attributes: ['file_name', 'file_url', 'file_key'],
	});

	return file;
};

const getAllFiles = async () => {
	const files = await db.tbl_common_file.findAll({
		attributes: ['file_name', 'file_url'],
	});
	return files;
};

const deleteFileByName = async (file_name, file_key) => {
	await db.tbl_common_file.destroy({ where: { file_name } });

	// delete from aws s3
	await deleteFromS3(file_key);
	return true;
};

module.exports = { getFileByName, getAllFiles, createFile, deleteFileByName };
