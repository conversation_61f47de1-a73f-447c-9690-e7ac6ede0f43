/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up(queryInterface, Sequelize) {
		const tableDesc = await queryInterface.describeTable('tbl_dealers');

		if (!tableDesc.city) {
			await queryInterface.addColumn('tbl_dealers', 'city', {
				type: Sequelize.STRING,
				allowNull: true,
			});
		}

		if (!tableDesc.state) {
			await queryInterface.addColumn('tbl_dealers', 'state', {
				type: Sequelize.STRING,
				allowNull: true,
			});
		}

		if (!tableDesc.country) {
			await queryInterface.addColumn('tbl_dealers', 'country', {
				type: Sequelize.STRING,
				allowNull: true,
			});
		}

		if (!tableDesc.zip) {
			await queryInterface.addColumn('tbl_dealers', 'zip', {
				type: Sequelize.STRING,
				allowNull: true,
			});
		}

		if (!tableDesc.personal_email) {
			await queryInterface.addColumn('tbl_dealers', 'personal_email', {
				type: Sequelize.STRING,
				allowNull: true,
			});
		}
	},

	async down(queryInterface) {
		const tableDesc = await queryInterface.describeTable('tbl_dealers');

		if (tableDesc.city) {
			await queryInterface.removeColumn('tbl_dealers', 'city');
		}

		if (tableDesc.state) {
			await queryInterface.removeColumn('tbl_dealers', 'state');
		}

		if (tableDesc.zip) {
			await queryInterface.removeColumn('tbl_dealers', 'zip');
		}

		if (tableDesc.country) {
			await queryInterface.removeColumn('tbl_dealers', 'country');
		}

		if (tableDesc.personal_email) {
			await queryInterface.removeColumn('tbl_dealers', 'personal_email');
		}
	},
};
