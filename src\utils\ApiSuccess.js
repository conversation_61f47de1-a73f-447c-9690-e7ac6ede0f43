class ApiSuccess {
	constructor(res, statusCode, message, data, ...moreData) {
		const respObj = {};
		respObj.code = statusCode || 200;
		respObj.message = message || 'success';
		respObj.success = true;

		// Only include `data` if it's not null/undefined/empty object
		if (
			data !== undefined &&
			data !== null &&
			!(
				typeof data === 'object' &&
				!Array.isArray(data) &&
				Object.keys(data).length === 0
			)
		) {
			respObj.data = data;
		}

		if (moreData.length > 0) {
			Object.assign(respObj, ...moreData);
		}

		res.status(statusCode || 200).json(respObj);
	}
}

module.exports = ApiSuccess;
