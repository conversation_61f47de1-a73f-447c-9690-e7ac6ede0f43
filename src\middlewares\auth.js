const jwt = require('jsonwebtoken');
const httpStatus = require('http-status');
const config = require('../config/config');

const authenticateJWT = (req, res, next) => {
	try {
		const authHeader = req.headers.authorization;
		// console.log('Auth Header in custom middleware:', authHeader);

		if (!authHeader || !authHeader.startsWith('Bearer ')) {
			return res.status(httpStatus.UNAUTHORIZED).json({
				success: false,
				message: 'Authorization header missing or malformed',
			});
		}

		const token = authHeader.split(' ')[1];
		if (!token) {
			return res.status(httpStatus.UNAUTHORIZED).json({
				success: false,
				message: 'Bearer token is missing',
			});
		}

		jwt.verify(token, config.jwt.secret, (err, decoded) => {
			if (err) {
				return res.status(httpStatus.UNAUTHORIZED).json({
					success: false,
					message: 'Invalid token',
					error: err.message,
				});
			}

			// console.log('Decoded token:', decoded);
			req.user = decoded;
			next();
		});
	} catch (error) {
		// console.error('Auth middleware error:', error);
		return res.status(httpStatus.INTERNAL_SERVER_ERROR).json({
			success: false,
			message: 'Authentication error',
			error: error.message,
		});
	}
};

module.exports = { authenticateJWT };
