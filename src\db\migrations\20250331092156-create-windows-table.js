/** @type {import('sequelize-cli').Migration} */
module.exports = {
	up: async (queryInterface, Sequelize) => {
		await queryInterface.createTable('tbl_windows', {
			id: {
				type: Sequelize.INTEGER,
				allowNull: false,
				primaryKey: true,
				autoIncrement: true,
			},
			room_id: {
				type: Sequelize.INTEGER,
				allowNull: false,
				references: {
					model: 'tbl_rooms', // Reference to the rooms table
					key: 'id',
				},
				onDelete: 'CASCADE',
			},
			window_name: {
				type: Sequelize.STRING,
				allowNull: false,
			},
			width: {
				type: Sequelize.FLOAT,
				allowNull: false,
			},
			height: {
				type: Sequelize.FLOAT,
				allowNull: false,
			},
			image_url: {
				type: Sequelize.STRING,
				allowNull: false,
			},
			is_delete: {
				type: Sequelize.BOOLEAN,
				defaultValue: false,
			},
			created_by: {
				type: Sequelize.STRING,
				allowNull: true,
			},
			created_date: {
				type: Sequelize.DATE,
				defaultValue: Sequelize.NOW,
			},
			updated_date: {
				type: Sequelize.DATE,
				defaultValue: Sequelize.NOW,
			},
		});
	},

	down: async (queryInterface /* , Sequelize */) => {
		await queryInterface.dropTable('tbl_windows');
	},
};
