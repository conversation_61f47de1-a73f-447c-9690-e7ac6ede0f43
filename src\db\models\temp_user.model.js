// c:\Users\<USER>\Desktop\your_project\models\tbl_temp_user.js

module.exports = (sequelize, DataTypes) => {
	const TblTempUser = sequelize.define(
		'tbl_temp_user',
		{
			id: {
				type: DataTypes.INTEGER,
				autoIncrement: true,
				primaryKey: true,
				allowNull: false,
			},
			first_name: {
				type: DataTypes.STRING(400),
				allowNull: false,
			},
			last_name: {
				type: DataTypes.STRING(400),
				allowNull: true,
			},
			mobile_no: {
				type: DataTypes.STRING(15),
				allowNull: true,
			},
			primary_email: {
				type: DataTypes.STRING(400),
				allowNull: true,
			},
			password: {
				type: DataTypes.STRING(255),
				allowNull: true,
			},
			unique_id: {
				type: DataTypes.STRING(400),
				allowNull: false,
			},
			otp: {
				type: DataTypes.STRING(6),
				allowNull: true,
			},
			created_date: {
				type: DataTypes.DATE,
				defaultValue: DataTypes.NOW,
				allowNull: false,
			},
			created_by: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
		},
		{
			tableName: 'tbl_temp_user',
			timestamps: false, // Set to true if you want Sequelize to manage createdAt and updatedAt fields
		}
	);

	TblTempUser.associate = function () {
		// associations can be defined here
	};

	return TblTempUser;
};
