// c:\Users\<USER>\Desktop\your_project\models\tbl_dealer_roles.js

module.exports = (sequelize, DataTypes) => {
	const TblDealerRoles = sequelize.define(
		'tbl_dealer_roles',
		{
			id: {
				type: DataTypes.INTEGER,
				autoIncrement: true,
				primaryKey: true,
				allowNull: false,
			},
			role_name: {
				type: DataTypes.STRING(255),
				allowNull: false,
			},
			slug: {
				type: DataTypes.STRING(255),
				allowNull: false,
			},
			created_date: {
				type: DataTypes.DATE,
				defaultValue: DataTypes.NOW,
				allowNull: false,
			},
			created_by: {
				type: DataTypes.INTEGER,
				allowNull: false,
			},
			updated_date: {
				type: DataTypes.DATE,
				allowNull: true,
			},
			updated_by: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			dealer_id: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			is_active: {
				type: DataTypes.INTEGER,
				defaultValue: 1,
				allowNull: false,
			},
			is_deleted: {
				type: DataTypes.BOOLEAN,
				defaultValue: false,
			},
			deleted_by: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			deleted_date: {
				type: DataTypes.DATE,
				allowNull: true,
			},
		},
		{
			tableName: 'tbl_dealer_roles',
			timestamps: false, // Set to true if you want Sequelize to manage createdAt and updatedAt fields
		}
	);

	TblDealerRoles.associate = function (models) {
		TblDealerRoles.belongsTo(models.tbl_dealers, {
			foreignKey: 'dealer_id',
			targetKey: 'id',
			as: 'dealer', // Optional alias
		});
	};

	return TblDealerRoles;
};
