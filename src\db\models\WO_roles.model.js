// c:\Users\<USER>\Desktop\your_project\models\tbl_wo_roles.js

module.exports = (sequelize, DataTypes) => {
	const TblWoRoles = sequelize.define(
		'tbl_wo_roles',
		{
			id: {
				type: DataTypes.INTEGER,
				autoIncrement: true,
				primaryKey: true,
				allowNull: false,
			},
			role_name: {
				type: DataTypes.STRING(255),
				allowNull: false,
			},
			slug: {
				type: DataTypes.STRING(255),
				allowNull: false,
			},
			created_date: {
				type: DataTypes.DATE,
				defaultValue: DataTypes.NOW,
				allowNull: false,
			},
			created_by: {
				type: DataTypes.INTEGER,
				allowNull: false,
				defaultValue: DataTypes.NOW,
			},
			updated_date: {
				type: DataTypes.DATE,
				allowNull: true,
			},
			updated_by: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			is_active: {
				type: DataTypes.INTEGER,
				defaultValue: 1,
				allowNull: false,
			},
			is_deleted: {
				type: DataTypes.BOOLEAN,
				defaultValue: false,
			},
			deleted_by: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			deleted_date: {
				type: DataTypes.DATE,
				allowNull: true,
			},
		},
		{
			tableName: 'tbl_wo_roles',
			timestamps: true, // Enables createdAt and updatedAt
			paranoid: true, // Enables deletedAt
			createdAt: 'created_date', // Rename to match your database
			updatedAt: 'updated_date',
			deletedAt: 'deleted_date',
		}
	);

	TblWoRoles.associate = function () {
		// associations can be defined here
	};

	return TblWoRoles;
};
