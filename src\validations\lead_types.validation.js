const Joi = require('@hapi/joi');

const leadTypes = {
	body: Joi.object().keys({
		title: Joi.string().required(),
		is_status: Joi.boolean().default(false),
	}),
};

const updateSortSchema = {
	body: Joi.array()
		.items(
			Joi.object({
				id: Joi.number().integer().positive().required().messages({
					'number.base': 'ID must be a number.',
					'number.integer': 'ID must be an integer.',
					'number.positive': 'ID must be a positive number.',
					'any.required': 'ID is required.',
				}),
				sort: Joi.number().integer().positive().required().messages({
					'number.base': 'Sort must be a number.',
					'number.integer': 'Sort must be an integer.',
					'number.positive': 'Sort must be a positive number.',
					'any.required': 'Sort is required.',
				}),
			})
		)
		.min(1)
		.required()
		.messages({
			'array.base': 'Sort values must be an array.',
			'array.min': 'At least one sort object is required.',
			'any.required': 'Sort values must be an array and it is required.',
		}),
};

module.exports = {
	leadTypes,
	updateSortSchema,
};
