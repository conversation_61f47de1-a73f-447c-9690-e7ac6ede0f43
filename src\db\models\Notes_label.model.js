'use_strict';

module.exports = (sequelize, DataTypes) => {
	const notesLabel = sequelize.define(
		'tbl_notes_label',
		{
			id: {
				type: DataTypes.INTEGER,
				autoIncrement: true,
				primaryKey: true,
				allowNull: false,
			},
			title: {
				type: DataTypes.STRING(100),
				allowNull: false,
				unique: true,
			},
			sort: {
				type: DataTypes.INTEGER,
				allowNull: false,
				defaultValue: 0,
			},
			is_status: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: false, // 0 means not deleted
				comment: 'true = deleted, false = not deleted', // Add comment
			},
			context: {
				type: DataTypes.STRING(100),
				allowNull: false,
			},
			created_by: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			created_date: {
				type: DataTypes.DATE,
				allowNull: false,
				defaultValue: DataTypes.NOW,
			},
			updated_by: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			updated_date: {
				type: DataTypes.DATE,
				allowNull: true,
			},
			deleted: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: false, // 0 means not deleted
				comment: 'true = deleted, false = not deleted', // Add comment
			},
			deleted_by: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			deleted_date: {
				type: DataTypes.DATE,
				allowNull: true,
			},
		},
		{
			tableName: 'tbl_notes_label',
			timestamps: false, // Using custom created_date and updated_date
			hooks: {
				// eslint-disable-next-line no-shadow
				beforeCreate: async (notesLabel, options) => {
					// eslint-disable-next-line no-param-reassign
					notesLabel.created_date = new Date();
					if (options.userId) {
						// eslint-disable-next-line no-param-reassign
						notesLabel.created_by = options.userId;
					}

					// Auto-increment sort
					const lastNotesLabel = await notesLabel.constructor.findOne({
						order: [['sort', 'DESC']],
						attributes: ['sort'],
					});

					// eslint-disable-next-line no-param-reassign
					notesLabel.sort = lastNotesLabel ? lastNotesLabel.sort + 1 : 1;
				},
				// eslint-disable-next-line no-shadow
				beforeUpdate: (notesLabel, options) => {
					// eslint-disable-next-line no-param-reassign
					notesLabel.updated_date = new Date();
					if (options.userId) {
						// eslint-disable-next-line no-param-reassign
						notesLabel.updated_by = options.userId;
					}
				},
			},
		}
	);

	return notesLabel;
};
