/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up(queryInterface, Sequelize) {
		const tableDescription = await queryInterface.describeTable('tbl_users');
		if (!tableDescription.dealer_homeowner) {
			await queryInterface.addColumn('tbl_users', 'dealer_homeowner', {
				type: Sequelize.BOOLEAN,
				allowNull: false,
				defaultValue: false,
			});
		}
	},

	async down(queryInterface) {
		const tableDescription = await queryInterface.describeTable('tbl_users');
		if (!tableDescription.dealer_homeowner) {
			await queryInterface.removeColumn('tbl_users', 'dealer_homeowner');
		}
	},
};
