module.exports = (sequelize, DataTypes) => {
	const TblWindows = sequelize.define(
		'tbl_windows',
		{
			id: {
				type: DataTypes.INTEGER,
				autoIncrement: true,
				primaryKey: true,
				allowNull: false,
			},
			room_id: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'tbl_rooms', // Ensure this matches the table name in DB
					key: 'id',
				},
				onDelete: 'CASCADE',
			},
			window_name: {
				type: DataTypes.STRING(255),
				allowNull: false,
			},
			width: {
				type: DataTypes.FLOAT,
				allowNull: false,
			},
			height: {
				type: DataTypes.FLOAT,
				allowNull: false,
			},
			image_url: {
				type: DataTypes.STRING,
				allowNull: false,
			},
			is_delete: {
				type: DataTypes.BOOLEAN,
				defaultValue: false,
			},
			created_by: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			created_date: {
				type: DataTypes.DATE,
				defaultValue: DataTypes.NOW,
				allowNull: false,
			},
			updated_date: {
				type: DataTypes.DATE,
				//  defaultValue: DataTypes.NOW,
				allowNull: true,
			},
			s3_key: {
				type: DataTypes.STRING,
				allowNull: true,
			},
		},
		{
			tableName: 'tbl_windows',
			timestamps: false,
		}
	);

	TblWindows.associate = (models) => {
		TblWindows.belongsTo(models.tbl_rooms, {
			foreignKey: 'room_id',
			targetKey: 'id',
			onDelete: 'CASCADE',
			as: 'room', // Optional alias
		});
	};

	return TblWindows;
};
