const httpStatus = require('http-status');
const bcrypt = require('bcrypt');
const { Op } = require('sequelize');
const ApiError = require('../utils/ApiError');
const { encryptData } = require('../utils/auth');
const db = require('../db/models');

async function getUserByEmail(email) {
	const user = await db.tbl_users.findOne({
		where: { primary_email: email },
		include: [
			{
				model: db.tbl_wo_roles,
				as: 'role', // ✅ This must match the alias in `tbl_users.js`
				require: true,
				attributes: ['id', 'role_name'],
			},
		],
		raw: true,
	});
	if (!user) {
		return { success: false, message: "The Email Id isn't present." };
	}
	return { success: true, user };
}

// async function getUserById(id) {
// 	const user = await db.user.findByPk({
// 		where: { id },
// 		include: [
// 			{
// 				model: db.role,
// 				require: true,
// 				attributes: ['id', 'name'],
// 			},
// 		],
// 		raw: true,
// 	});

// 	return user;
// }
async function getUserById(user_id) {
	try {
		return await db.tbl_users.findByPk(user_id); // Fetch user by ID
	} catch (error) {
		throw new Error(error.message);
	}
}

async function updateUser(user_id, userData) {
	try {
		await db.tbl_users.update(userData, {
			where: { id: user_id },
		});
	} catch (error) {
		throw new Error(error.message);
	}
}

async function updateHomeowner(home_owner_id, homeownerData) {
	try {
		await db.tbl_home_owners.update(homeownerData, {
			where: { id: home_owner_id },
		});
	} catch (error) {
		throw new Error(error.message);
	}
}
async function updateDealer(dealer_id, dealerData) {
	try {
		await db.tbl_dealers.update(dealerData, {
			where: { id: dealer_id },
		});
	} catch (error) {
		throw new Error(error.message);
	}
}

/**
 * Change user password
 * @param {number} userId - ID of the user
 * @param {string} currentPassword - Current password of the user
 * @param {string} newPassword - New password to be set
 * @returns {Promise<Object>} - Success message
 */
const changePassword = async (userId, currentPassword, newPassword) => {
	// Fetch the user from the database
	const user = await db.tbl_users.findByPk(userId);
	if (!user) {
		throw new ApiError(httpStatus.NOT_FOUND, 'User not found');
	}

	// Check if the current password is correct
	const isPasswordMatch = await bcrypt.compare(currentPassword, user.password);
	if (!isPasswordMatch) {
		throw new ApiError(
			httpStatus.UNAUTHORIZED,
			'Current password is incorrect'
		);
	}

	// Hash the new password
	const hashedNewPassword = await bcrypt.hash(newPassword, 10);

	// Update the password in the database
	await db.tbl_users.update(
		{ password: hashedNewPassword },
		{ where: { id: userId } }
	);

	return { success: true, message: 'Password changed successfully' };
};
// async function createUser(req) {
// 	const { email, name, password, roleId } = req.body;
// 	const hashedPassword = await encryptData(password);
// 	const user = await getUserByEmail(email);
// 	console.log("user at createUser():",user);

// 	if (user) {
// 		throw new ApiError(httpStatus.CONFLICT, 'This email already exits');
// 	}

// 	const role = await roleService.getRoleById(roleId);
// 	console.log("Role at createUser():",role);

// 	if (!role) {
// 		throw new ApiError(httpStatus.NOT_FOUND, 'Role not found');
// 	}

// 	const createdUser = await db.user
// 		.create({
// 			name,
// 			email,
// 			role_id: roleId,
// 			password: hashedPassword,
// 		})
// 		.then((resultEntity) => resultEntity.get({ plain: true }));

// 	return createdUser;
// }

// async function getUsers(req) {
// 	const { page: defaultPage, limit: defaultLimit } = config.pagination;
// 	const { page = defaultPage, limit = defaultLimit } = req.query;

// 	const offset = getOffset(page, limit);
// 	console.log("offset :", offset);

// 	const users = await db.user.findAndCountAll({
// 		order: [
// 			['name', 'ASC'],
// 			['created_date_time', 'DESC'],
// 			['modified_date_time', 'DESC'],
// 		],
// 		include: [
// 			{
// 				model: db.role,
// 				require: true,
// 				attributes: ['id', 'name'],
// 			},
// 		],
// 		attributes: [
// 			'id',
// 			'name',
// 			'email',
// 			'created_date_time',
// 			'modified_date_time',
// 		],
// 		offset,
// 		limit,
// 		raw: true,
// 	});

// 	return users;
// }

// async function deleteUserById(userId) {
// 	const deletedUser = await db.user.destroy({
// 		where: { id: userId },
// 	});

// 	if (!deletedUser) {
// 		throw new ApiError(httpStatus.NOT_FOUND, 'User not found');
// 	}

// 	return deletedUser;
// }

// async function updateUser(req) {
// 	const { password, email } = req.body;

// 	if (password) {
// 		const hashedPassword = await encryptData(password);

// 		if (!hashedPassword) {
// 			throw new ApiError(
// 				httpStatus.INTERNAL_SERVER_ERROR,
// 				'Internal Server Error'
// 			);
// 		}

// 		req.body.password = hashedPassword;
// 	}

// 	if (email) {
// 		const existedUser = await getUserByEmail(email);

// 		if (existedUser) {
// 			throw new ApiError(
// 				httpStatus.CONFLICT,
// 				'This email is already exist'
// 			);
// 		}
// 	}

// 	const updatedUser = await db.user
// 		.update(
// 			{ ...req.body },
// 			{
// 				where: { id: req.params.userId || req.body.id },
// 				returning: true,
// 				plain: true,
// 				raw: true,
// 			}
// 		)
// 		.then((data) => data[1]);

// 	return updatedUser;
// }

async function updateLogAccess(email) {
	const result = await db.tbl_users.update(
		{ token: null },
		{ where: { primary_email: email } }
	);
	return result;
}

async function updatePassword(email, newPassword, user) {
	if (!user) return false;
	const hashedPassword = await encryptData(newPassword);
	const emailTrim = email.trim();
	const sql = `
		UPDATE tbl_users
		SET 
		password = '${hashedPassword}',
		reset_password_token = null,
		reset_password_expires = null
		WHERE primary_email = '${emailTrim}'
	`;
	// eslint-disable-next-line no-unused-vars
	const [result, metadata] = await db.sequelize.query(
		sql,
		{ type: db.Sequelize.QueryTypes.UPDATE },
		// eslint-disable-next-line no-console
		{ logging: console.log }
	);
	return metadata;
}
// update reset tokens
async function updateResetTokens(email, resetToken) {
	const resetPasswordExpires = new Date(
		Date.now() + 24 * 60 * 60 * 1000
	).toISOString();
	const emailTrim = email.trim();
	const sql = `
		UPDATE tbl_users
		SET reset_password_token = '${resetToken}',
		reset_password_expires = '${resetPasswordExpires}'
		WHERE primary_email = '${emailTrim}'
	`;
	// eslint-disable-next-line no-unused-vars
	const [result, metadata] = await db.sequelize.query(
		sql,
		{ type: db.Sequelize.QueryTypes.UPDATE },
		// eslint-disable-next-line no-console
		{ logging: console.log }
	);
	return metadata;
}

async function getUserByToken(token) {
	const user = await db.tbl_users.findOne({
		where: {
			reset_password_token: token,
			reset_password_expires: {
				[Op.and]: [
					{ [Op.not]: null }, // Ensure it's not null
					{ [Op.gt]: new Date().toISOString() }, // Ensure expiry is greater than now
				],
			},
		},
		include: [
			{
				model: db.tbl_wo_roles,
				as: 'role', // This must match the alias in `tbl_users.js`
				require: true,
				attributes: ['id', 'role_name'],
			},
		],
		raw: true,
		// logging: console.log // This will print the query
	});
	if (!user) {
		return { success: false, message: 'Invalid is or expired token.' };
	}
	return { success: true, user };
}

async function getUserByToken_otp(UniqueId, otp) {
	const user = await db.tbl_users.findOne({
		// where: { unique_id : UniqueId,
		// 	verification_expiry: {
		// 		[Op.and]: [
		// 			{ [Op.not]: null },  // Ensure it's not null
		// 			{ [Op.gt]: new Date().toISOString() }  // Ensure expiry is greater than now
		// 		]
		// 	},
		// 	otp: otp // Add the condition for otp
		//  },
		where: {
			unique_id: UniqueId,

			otp, // Add the condition for otp
		},
		include: [
			{
				model: db.tbl_wo_roles,
				as: 'role', // This must match the alias in `tbl_users.js`
				require: true,
				attributes: ['id', 'role_name'],
			},
		],
		raw: true,
		// logging: console.log // This will print the query
	});
	if (!user) {
		return { success: false, message: 'Invalid is or expired token.' };
	}
	return { success: true, user };
}

/**
 * Create or update login PIN
 * @param {string} pin - 6-digit PIN
 * @param {number} userId - User ID
 * @returns {Promise<Object>}
 */
const createLoginPin = async (pin, userId) => {
	let transaction;

	try {
		// Initialize transaction
		transaction = await db.sequelize.transaction();

		// Check if user exists
		const user = await db.tbl_users.findByPk(userId, { transaction });
		if (!user) {
			throw new ApiError(httpStatus.NOT_FOUND, 'User not found');
		}

		// First, get all users with PINs
		const usersWithPins = await db.tbl_users.findAll({
			where: {
				id: { [db.Sequelize.Op.ne]: userId },
				login_pin: { [db.Sequelize.Op.ne]: null },
			},
			transaction,
		});

		// Check each user's PIN
		// eslint-disable-next-line no-restricted-syntax
		for (const existingUser of usersWithPins) {
			// eslint-disable-next-line no-await-in-loop
			const isPinMatch = await bcrypt.compare(pin, existingUser.login_pin);
			if (isPinMatch) {
				throw new ApiError(
					httpStatus.CONFLICT,
					'This PIN is already in use. Please choose a different PIN'
				);
			}
		}

		// If we get here, the PIN is unique. Hash and save it
		const hashedPin = await bcrypt.hash(pin, 10);

		// Update user's PIN
		const [updatedRows] = await db.tbl_users.update(
			{ login_pin: hashedPin },
			{
				where: { id: userId },
				transaction,
			}
		);

		if (updatedRows === 0) {
			throw new ApiError(
				httpStatus.INTERNAL_SERVER_ERROR,
				'Failed to update PIN - No rows affected'
			);
		}

		// Commit transaction
		await transaction.commit();

		return {
			userId: user.id,
			updated: true,
		};
	} catch (error) {
		// Rollback transaction on error
		if (transaction) await transaction.rollback();
		throw error;
	}
};

module.exports = {
	getUserByEmail,
	getUserById,
	updateUser,
	updateHomeowner,
	updateDealer,
	changePassword,
	// createUser,
	// updateUser,
	// getUsers,
	// deleteUserById,
	updateLogAccess,
	updatePassword,
	updateResetTokens,
	getUserByToken,
	getUserByToken_otp,
	createLoginPin,
};
