/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up(queryInterface, Sequelize) {
		const table = await queryInterface.describeTable('tbl_notes');

		if (table.labels) {
			await queryInterface.removeColumn('tbl_notes', 'labels');
		}

		// Add new labels column (INTEGER[])
		await queryInterface.addColumn('tbl_notes', 'labels', {
			type: Sequelize.ARRAY(Sequelize.INTEGER),
			allowNull: true,
			defaultValue: [],
		});
	},

	async down(queryInterface, Sequelize) {
		const table = await queryInterface.describeTable('tbl_notes');
		// Drop integer[] labels column
		if (table.labels) {
			await queryInterface.removeColumn('tbl_notes', 'labels');
		}

		// Add back as string[] (revert)
		await queryInterface.addColumn('tbl_notes', 'labels', {
			type: Sequelize.ARRAY(Sequelize.STRING),
			allowNull: true,
			defaultValue: [],
		});
	},
};
