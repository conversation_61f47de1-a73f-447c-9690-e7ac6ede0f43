const Joi = require('@hapi/joi');

const changePassword = {
	body: Joi.object().keys({
		currentPassword: Joi.string().required(),
		newPassword: Joi.string()
			.required()
			.min(8)
			.max(32) // Optional: Set a max length
			.regex(
				/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]+$/
			)
			.messages({
				'string.min': 'Password must be at least 8 characters long.',
				'string.max': 'Password must not exceed 32 characters.',
				'string.pattern.base':
					'Password must include at least one uppercase letter, one lowercase letter, one digit, and one special character (@$!%*?&).',
				'any.required': 'Password is required.',
			}),
		confirmPassword: Joi.string()
			.required()
			.valid(Joi.ref('newPassword'))
			.messages({
				'any.only': 'Confirm password must match password',
			}),
	}),
};

const validateUserInfo = {
	body: Joi.object().keys({
		firstName: Joi.string().required().max(255).messages({
			'string.empty': 'First name is required',
			'any.required': 'First name is required',
		}),
		lastName: Joi.string().allow('', null).optional().max(255),
		mobileNo: Joi.string()
			.pattern(/^[0-9]+$/)
			.required()
			.messages({
				'string.pattern.base': 'Mobile number must contain only digits',
				'string.empty': 'Mobile number is required',
			}),
		emailId: Joi.string().required().email().max(255).messages({
			'string.email': 'Email must be a valid email address',
			'any.required': 'Email is required',
		}),
		website: Joi.string()
			.uri()
			.allow('', null) // Allow empty values
			.optional()
			.messages({
				'string.uri': 'Website must be a valid URL',
			}),
		companyName: Joi.string().allow('', null).optional().max(255),
		companyPhone: Joi.string()
			.pattern(/^\+?[1-9]\d{1,14}$/) // E.164 format for international phone numbers
			.allow('', null) // Allow empty values
			.optional()
			.messages({
				'string.pattern.base': 'Company phone must be a valid phone number',
			}),
		address: Joi.string().allow('', null).optional().max(500).messages({
			'string.max': 'Address must not exceed 500 characters',
		}),
		city: Joi.string().allow('', null).optional().max(100).messages({
			'string.max': 'City must not exceed 100 characters',
		}),
		state: Joi.string().allow('', null).optional().max(100).messages({
			'string.max': 'State must not exceed 100 characters',
		}),
		country: Joi.string().allow('', null).optional().max(100).messages({
			'string.max': 'Country must not exceed 100 characters',
		}),
		zip: Joi.string()
			.allow('', null)
			.pattern(/^[0-9]{5}(-[0-9]{4})?$/) // US ZIP code format
			.optional()
			.messages({
				'string.pattern.base':
					'Zip code must be in the format 12345 or 12345-6789',
			}),
		image_base64: Joi.string().allow('', null).optional().messages({
			'string.empty': 'Image base64 string is optional',
		}),
	}),
};

const getUsers = {
	query: Joi.object().keys({
		name: Joi.string(),
		email: Joi.string().email(),
		roleId: Joi.number(),
		limit: Joi.number().min(1),
		page: Joi.number().min(1),
	}),
};

const getUser = {
	params: Joi.object().keys({
		userId: Joi.string(),
	}),
};

// const updateUser = {
// 	params: Joi.object().keys({
// 		userId: Joi.required(),
// 	}),
// 	body: Joi.object()
// 		.keys({
// 			email: Joi.string().email(),
// 			password: Joi.string().custom(password),
// 			name: Joi.string(),
// 		})
// 		.min(1),
// };

const deleteUser = {
	params: Joi.object().keys({
		userId: Joi.string(),
	}),
};

const createLoginPin = {
	body: Joi.object().keys({
		pin: Joi.string()
			.pattern(/^[0-9]{6}$/)
			.required()
			.messages({
				'string.pattern.base': 'PIN must be exactly 6 digits',
				'string.empty': 'PIN is required',
				'any.required': 'PIN is required',
			}),
	}),
};
module.exports = {
	changePassword,
	validateUserInfo,
	getUsers,
	getUser,
	// updateUser,
	deleteUser,
	createLoginPin,
};
