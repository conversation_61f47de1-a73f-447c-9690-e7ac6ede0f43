module.exports = (sequelize, DataTypes) => {
	const TblDealerFiles = sequelize.define(
		'tbl_files',
		{
			id: {
				type: DataTypes.INTEGER,
				autoIncrement: true,
				primaryKey: true,
				allowNull: false,
			},
			dealer_id: {
				type: DataTypes.INTEGER,
				allowNull: true,
				references: {
					model: 'tbl_dealers',
					key: 'id',
				},
				onDelete: 'CASCADE',
			},
			lead_id: {
				type: DataTypes.INTEGER,
				allowNull: true,
				references: {
					model: 'tbl_users',
					key: 'id',
				},
				onDelete: 'CASCADE',
			},
			file_name: {
				type: DataTypes.STRING,
				allowNull: false,
			},
			file_type: {
				type: DataTypes.STRING,
				allowNull: false,
			},
			file_size: {
				type: DataTypes.INTEGER,
				allowNull: false,
			},
			file_url: {
				type: DataTypes.TEXT,
				allowNull: false,
			},
			file_key: {
				type: DataTypes.TEXT,
				allowNull: false,
			},
			added_by: {
				type: DataTypes.INTEGER,
				allowNull: false,
			},
			deleted_by: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			is_deleted: {
				type: DataTypes.BOOLEAN,
				defaultValue: false,
				allowNull: false,
			},
			deleted_at: {
				type: DataTypes.DATE,
				allowNull: true,
			},
		},
		{
			tableName: 'tbl_files',
			timestamps: true,
			paranoid: true,
			createdAt: 'created_at',
			updatedAt: 'updated_at',
			deletedAt: 'deleted_at',
		}
	);

	TblDealerFiles.associate = (models) => {
		TblDealerFiles.belongsTo(models.tbl_dealers, {
			foreignKey: 'dealer_id',
			as: 'dealerFile',
			onDelete: 'CASCADE',
		});
	};

	TblDealerFiles.addHook('beforeDestroy', async (file, options) => {
		const { deleted_by } = options; // Pass deleted_by through options
		await TblDealerFiles.update(
			{ is_deleted: true, deleted_by },
			{ where: { id: file.id } }
		);
	});

	// Hook to reset `is_deleted` and `deleted_by` on restore
	TblDealerFiles.addHook('beforeRestore', async (file, options) => {
		const updatedFile = {
			is_deleted: false,
			deleted_by: null,
		};

		if (options.modified_by) {
			updatedFile.modified_by = options.modified_by;
		}

		await TblDealerFiles.update(updatedFile, {
			where: { id: file.id },
		});
	});

	// Hook to update `modified_by` when updating the record
	TblDealerFiles.addHook('beforeUpdate', async (file, options) => {
		const { modified_by } = options; // Pass modified_by through options
		await TblDealerFiles.update({ modified_by }, { where: { id: file.id } });
	});
	return TblDealerFiles;
};
