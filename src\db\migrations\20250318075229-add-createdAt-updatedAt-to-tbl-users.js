/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up(queryInterface, Sequelize) {
		const tableDefinition = await queryInterface.describeTable('tbl_users');

		if (!tableDefinition.created_at) {
			await queryInterface.addColumn('tbl_users', 'created_at', {
				type: Sequelize.DATE,
				allowNull: false,
				defaultValue: Sequelize.fn('NOW'),
			});
		}

		if (!tableDefinition.updated_at) {
			await queryInterface.addColumn('tbl_users', 'updated_at', {
				type: Sequelize.DATE,
				allowNull: false,
				defaultValue: Sequelize.fn('NOW'),
			});
		}
	},

	async down(queryInterface) {
		const tableDefinition = await queryInterface.describeTable('tbl_users');

		if (tableDefinition.createdAt) {
			await queryInterface.removeColumn('tbl_users', 'createdAt');
		}

		// ✅ Remove updatedAt if it exists
		if (tableDefinition.updatedAt) {
			await queryInterface.removeColumn('tbl_users', 'updatedAt');
		}
	},
};
