const httpStatus = require('http-status');
const db = require('../db/models');
const ApiError = require('../utils/ApiError');
const {
	uploadFileToAwsS3,
	uploadSingleFileToAwsS3,
	deleteFromS3,
} = require('../utils/awsFileUpload');

/**
 * Create a note.
 * @param {Object} noteBody - Note data.
 * @returns {Promise<Object>}
 */
const createNote = async (noteBody) => {
	let uploadedFiles = [];

	if (noteBody.files.length > 0) {
		const uploadPromises = await uploadFileToAwsS3(noteBody.files);
		uploadedFiles = await Promise.all(uploadPromises);
	}

	const note = await db.tbl_notes.create({
		...noteBody,
		files: uploadedFiles,
	});
	const plainNote = note.get({ plain: true });
	return plainNote;
};

/**
 * Get all notes by dealer ID.
 * @param {number} dealerId - Dealer ID.
 * @param {number} page - Page number.
 * @param {number} limit - Items per page.
 * @param {string} [search] - Search keyword.
 * @returns {Promise<Object>}
 */
const getAllNotesByDealerId = async (dealerId, page, limit, search) => {
	// eslint-disable-next-line no-param-reassign
	page = parseInt(page, 10) || 1;
	// eslint-disable-next-line no-param-reassign
	limit = parseInt(limit, 10) || 10;

	const offset = (page - 1) * limit;

	const { count, rows } = await db.tbl_notes.findAndCountAll({
		where: {
			dealer_id: dealerId,
			...(search && { title: { [db.Sequelize.Op.iLike]: `%${search}%` } }),
		},
		order: [['created_at', 'DESC']],
		limit,
		offset,
		attributes: [
			'id',
			'title',
			'labels',
			'description',
			'created_by',
			'created_at',
			[
				db.Sequelize.literal(`
                    (
                        SELECT jsonb_agg(jsonb_build_object(
                            'id', file->>'id',
                            'file_url', file->>'file_url',
                            'file_name', file->>'file_name',
                            'file_type', file->>'file_type'
                        ))
                        FROM jsonb_array_elements("tbl_notes"."files") AS file
                    )
                `),
				'files',
			],
		],
		raw: true,
	});

	// Fetch associated labels for each note
	const notes = await Promise.all(
		rows.map(async (note) => {
			const labels = await db.tbl_notes.prototype.getNoteLabels.call(note); // Use call to associate with the note instance
			return { ...note, labels };
		})
	);

	const totalPages = Math.ceil(count / limit);
	const nextPage = page < totalPages ? page + 1 : null;
	const prevPage = page > 1 ? page - 1 : null;

	return {
		notes,
		pagination: {
			totalRows: count,
			filteredRows: rows.length,
			currentPage: page,
			totalPages,
			nextPage,
			prevPage,
			hasNextPage: page < totalPages,
			hasPrevPage: page > 1,
		},
	};
};

/**
 * Get a note by ID.
 * @param {number} noteId - Note ID.
 * @returns {Promise<Object>}
 */
const getNoteById = async (noteId) => {
	const note = await db.tbl_notes.findByPk(noteId, {
		raw: true,
	});
	if (!note) {
		throw new ApiError(httpStatus.BAD_REQUEST, 'Note not found');
	}

	const labels = await db.tbl_notes.prototype.getNoteLabels.call(note); // or: await note.getNoteLabels();

	return {
		...note, // convert Sequelize instance to plain object
		labels,
	};
};

/**
 * Update a note by ID.
 * @param {number} noteId - Note ID.
 * @param {Object} updateBody - Updated data.
 * @returns {Promise<boolean>}
 */
const updateNoteById = async (noteId, updateBody, userId) => {
	const note = await getNoteById(noteId);

	const { files } = updateBody;

	const existingFiles = note.files || [];
	const updatedFiles = [];

	// Delete files that are missing in the request
	// eslint-disable-next-line no-restricted-syntax
	for (const file of existingFiles) {
		if (!files.some((f) => f.id === file.id)) {
			// eslint-disable-next-line no-await-in-loop
			await deleteFromS3(file.file_key); // delete from aws s3
		} else {
			updatedFiles.push(file); // Keep existing files
		}
	}

	// Upload new files
	// eslint-disable-next-line no-restricted-syntax
	for (const file of files) {
		if (!file.id && file.image) {
			file.folder_name = 'winco';
			// eslint-disable-next-line no-await-in-loop
			const uploadedFiles = await uploadSingleFileToAwsS3(file);
			updatedFiles.push(uploadedFiles);
		}
	}

	// eslint-disable-next-line no-param-reassign
	updateBody.files = updatedFiles;
	// eslint-disable-next-line no-param-reassign
	updateBody.modified_by = userId;

	await db.tbl_notes.update(updateBody, {
		where: { id: noteId },
	});

	return true;
};

/**
 * Delete a note by ID.
 * @param {number} noteId - Note ID.
 * @returns {Promise<boolean>}
 */
const deleteNoteById = async (noteId, userId) => {
	await getNoteById(noteId);
	// const existingFiles = note.files || []

	// delete file form aws s3
	// if (existingFiles.length > 0) {

	//     for (const file of existingFiles) {
	//         await deleteFromS3(file.file_key);
	//     }
	// }

	await db.tbl_notes.destroy({
		where: { id: noteId },
		individualHooks: true,
		deleted_by: userId,
	});

	return true;
};

/**
 * Get all notes by dealer ID.
 * @param {number} dealerId - Dealer ID.
 * @param {number} page - Page number.
 * @param {number} limit - Items per page.
 * @param {string} [search] - Search keyword.
 * @returns {Promise<Object>}
 */
const getAllNotesByLeadId = async (lead_id, page, limit, search) => {
	// eslint-disable-next-line no-param-reassign
	page = parseInt(page, 10) || 1;
	// eslint-disable-next-line no-param-reassign
	limit = parseInt(limit, 10) || 10;

	const offset = (page - 1) * limit;

	const { count, rows } = await db.tbl_notes.findAndCountAll({
		where: {
			lead_id,
			...(search && { title: { [db.Sequelize.Op.iLike]: `%${search}%` } }),
		},
		order: [['created_at', 'DESC']],
		limit,
		offset,
		attributes: [
			'id',
			'title',
			'description',
			'created_by',
			'created_at',
			[
				db.Sequelize.literal(`
                    (
                        SELECT jsonb_agg(jsonb_build_object(
                            'id', file->>'id',
                            'file_url', file->>'file_url',
                            'file_name', file->>'file_name',
                            'file_type', file->>'file_type'
                        ))
                        FROM jsonb_array_elements("tbl_notes"."files") AS file
                    )
                `),
				'files',
			],
		],
		raw: true,
	});

	const totalPages = Math.ceil(count / limit);
	const nextPage = page < totalPages ? page + 1 : null;
	const prevPage = page > 1 ? page - 1 : null;

	return {
		rows,
		pagination: {
			nextPage,
			prevPage,
			totalPages,
			totalItems: count,
			limit,
			hasNextPage: page < totalPages,
			hasPrevPage: page > 1,
		},
	};
};

module.exports = {
	createNote,
	getAllNotesByDealerId,
	getNoteById,
	updateNoteById,
	deleteNoteById,
	getAllNotesByLeadId,
};
