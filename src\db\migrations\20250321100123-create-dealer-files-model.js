/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up(queryInterface, Sequelize) {
		await queryInterface.createTable('tbl_dealer_files', {
			id: {
				allowNull: false,
				autoIncrement: true,
				primaryKey: true,
				type: Sequelize.INTEGER,
			},
			dealer_id: {
				type: Sequelize.INTEGER,
				allowNull: false,
				references: {
					model: 'tbl_dealers',
					key: 'id',
				},
				onDelete: 'CASCADE',
				onUpdate: 'CASCADE',
			},
			added_by: {
				type: Sequelize.INTEGER,
				allowNull: false,
			},
			file_name: {
				type: Sequelize.STRING,
				allowNull: false,
			},
			file_type: {
				type: Sequelize.STRING,
				allowNull: false,
			},
			file_size: {
				type: Sequelize.INTEGER,
				allowNull: false,
			},
			file_url: {
				type: Sequelize.TEXT,
				allowNull: false,
			},
			file_key: {
				type: Sequelize.TEXT,
				allowNull: false,
			},
			created_at: {
				allowNull: false,
				type: Sequelize.DATE,
			},
			updated_at: {
				allowNull: false,
				type: Sequelize.DATE,
			},
		});
	},
	async down(queryInterface) {
		await queryInterface.dropTable('tbl_dealer_files');
	},
};
