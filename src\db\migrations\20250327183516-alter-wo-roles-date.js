/** @type {import('sequelize-cli').Migration} */
module.exports = {
	up: async (queryInterface, Sequelize) => {
		const tableDesc = await queryInterface.describeTable('tbl_wo_roles');

		// Check if the column already has default value
		if (tableDesc.created_date.defaultValue !== 'CURRENT_TIMESTAMP') {
			return queryInterface.changeColumn('tbl_wo_roles', 'created_date', {
				type: Sequelize.DATE,
				allowNull: false,
				defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
			});
		}
	},

	down: async (queryInterface, Sequelize) => {
		return queryInterface.changeColumn('tbl_wo_roles', 'created_date', {
			type: Sequelize.DATE,
			allowNull: false,
			defaultValue: null, // Reverting change
		});
	},
};
