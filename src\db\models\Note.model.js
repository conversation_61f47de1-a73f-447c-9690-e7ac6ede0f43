const { Op } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
	const TblDealerNotes = sequelize.define(
		'tbl_notes',
		{
			id: {
				allowNull: false,
				autoIncrement: true,
				primaryKey: true,
				type: DataTypes.INTEGER,
			},
			title: {
				type: DataTypes.STRING,
				allowNull: false,
			},
			description: {
				type: DataTypes.TEXT,
				allowNull: true,
			},
			labels: {
				type: DataTypes.ARRAY(DataTypes.INTEGER),
				allowNull: true,
				defaultValue: [],
			},
			dealer_id: {
				type: DataTypes.INTEGER,
				allowNull: true,
				references: {
					model: 'tbl_dealers',
					key: 'id',
				},
				onDelete: 'CASCADE',
			},
			lead_id: {
				type: DataTypes.INTEGER,
				allowNull: true,
				references: {
					model: 'tbl_users',
					key: 'id',
				},
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			},
			files: {
				type: DataTypes.JSONB, // Use JSONB for PostgreSQL, JSON for other DBs
				allowNull: true,
				defaultValue: [],
				validate: {
					isValidFileArray(value) {
						if (value && !Array.isArray(value)) {
							throw new Error('File must be an array of objects');
						}
						if (
							value &&
							value.some(
								(file) =>
									!file.file_name ||
									!file.file_url ||
									!file.file_type ||
									!file.file_key ||
									!file.file_size ||
									!file.id
							)
						) {
							throw new Error(
								'Each file must have file_name, file_url, file_type, file_key,file_size, and id'
							);
						}
					},
				},
			},
			created_by: {
				type: DataTypes.INTEGER,
				allowNull: false,
			},
			modified_by: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			deleted_by: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			deleted_at: {
				type: DataTypes.DATE,
				allowNull: true,
			},
			is_deleted: {
				type: DataTypes.BOOLEAN,
				defaultValue: false,
				allowNull: false,
			},
		},
		{
			tableName: 'tbl_notes',
			timestamps: true,
			createdAt: 'created_at',
			updatedAt: 'updated_at',
			paranoid: true,
			deletedAt: 'deleted_at',
		}
	);

	TblDealerNotes.associate = (models) => {
		TblDealerNotes.belongsTo(models.tbl_dealers, {
			foreignKey: 'dealer_id',
			as: 'dealer',
			onDelete: 'CASCADE',
			onUpdate: 'CASCADE',
		});

		// Custom getter to fetch associated labels
		TblDealerNotes.prototype.getNoteLabels = async function (options) {
			if (this.labels && this.labels.length > 0) {
				return models.tbl_notes_label.findAll({
					where: {
						id: {
							[Op.in]: this.labels,
						},
						deleted: false, // Assuming you only want non-deleted labels
					},
					attributes: ['id', 'title'],
					...options, // Allow passing additional options like 'order'
				});
			}
			return [];
		};
	};

	TblDealerNotes.addHook('beforeDestroy', async (note, options) => {
		const { deleted_by } = options; // Pass deleted_by through options
		await TblDealerNotes.update(
			{ is_deleted: true, deleted_by },
			{ where: { id: note.id } }
		);
	});

	// Hook to reset `is_deleted` and `deleted_by` on restore
	TblDealerNotes.addHook('beforeRestore', async (note, options) => {
		// eslint-disable-next-line no-unused-expressions, no-param-reassign, no-sequences
		(note.is_deleted = false), (note.deleted_by = null);
		if (options.modified_by) {
			// eslint-disable-next-line no-param-reassign
			note.modified_by = options.modified_by;
		}
		await note.save();
	});

	// Hook to update `modified_by` when updating the record
	TblDealerNotes.addHook('beforeUpdate', async (note, options) => {
		const { modified_by } = options; // Pass modified_by through options
		await TblDealerNotes.update({ modified_by }, { where: { id: note.id } });
	});

	return TblDealerNotes;
};
