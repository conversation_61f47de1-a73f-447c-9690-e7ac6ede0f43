module.exports = (sequelize, DataTypes) => {
	const Installer = sequelize.define(
		'Installer',
		{
			id: {
				type: DataTypes.INTEGER,
				autoIncrement: true,
				primaryKey: true,
			},
			name: {
				type: DataTypes.STRING,
				allowNull: false,
			},
			email: {
				type: DataTypes.STRING,
				allowNull: false,
				validate: {
					isEmail: true,
				},
			},
			phone: {
				type: DataTypes.STRING(20),
				allowNull: false,
				unique: true,
			},
			dealer_id: {
				type: DataTypes.INTEGER,
				defaultValue: 1,
			},
			status: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			is_deleted: {
				type: DataTypes.BOOLEAN,
				defaultValue: false,
			},
			deleted_by: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			deleted_date: {
				type: DataTypes.DATE,
				allowNull: true,
			},
			created_by: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			created_date: {
				type: DataTypes.DATE,
				allowNull: false,
				defaultValue: DataTypes.NOW,
			},
			updated_by: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			updated_date: {
				type: DataTypes.DATE,
				allowNull: false,
				defaultValue: DataTypes.NOW,
			},
		},
		{
			tableName: 'tbl_installers',
			timestamps: false, // Since created_date and updated_date are manually managed
			hooks: {
				beforeCreate: async (installer, options) => {
					// eslint-disable-next-line no-param-reassign
					installer.created_date = new Date();
					if (options.userId) {
						// eslint-disable-next-line no-param-reassign
						installer.created_by = options.userId;
					}
				},
				beforeUpdate: (installer, options) => {
					// eslint-disable-next-line no-param-reassign
					installer.updated_date = new Date();
					if (options.userId) {
						// eslint-disable-next-line no-param-reassign
						installer.updated_by = options.userId;
					}
				},
			},
		}
	);

	return Installer;
};
