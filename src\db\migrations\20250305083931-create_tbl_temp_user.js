/** @type {import('sequelize-cli').Migration} */
// c:\Users\<USER>\Desktop\migrations\20231010_create_tbl_temp_user.js

module.exports = {
	up: (queryInterface, Sequelize) =>
		queryInterface.createTable('tbl_temp_user', {
			id: {
				type: Sequelize.INTEGER,
				allowNull: false,
				primaryKey: true,
				autoIncrement: true,
			},
			first_name: {
				type: Sequelize.ARRAY(Sequelize.STRING(400)),
				allowNull: false,
			},
			last_name: {
				type: Sequelize.ARRAY(Sequelize.STRING(400)),
				allowNull: true,
			},
			mobile_no: {
				type: Sequelize.STRING(15),
				allowNull: true,
			},
			primary_email: {
				type: Sequelize.ARRAY(Sequelize.STRING(400)),
				allowNull: true,
			},
			password: {
				type: Sequelize.STRING(255),
				allowNull: true,
			},
			unique_id: {
				type: Sequelize.ARRAY(Sequelize.STRING(400)),
				allowNull: false,
			},
			otp: {
				type: Sequelize.STRING(6),
				allowNull: true,
			},
			created_date: {
				type: Sequelize.DATE,
				defaultValue: Sequelize.NOW,
				allowNull: false,
			},
			created_by: {
				type: Sequelize.INTEGER,
				allowNull: true,
			},
		}),
	down: (queryInterface /* , Sequelize */) =>
		queryInterface.dropTable('tbl_temp_user'),
};
