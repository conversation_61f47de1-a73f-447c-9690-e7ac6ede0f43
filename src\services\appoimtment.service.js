const httpStatus = require('http-status');
const db = require('../db/models');
const ApiError = require('../utils/ApiError');
const {
	uploadFileToAwsS3,
	deleteFromS3,
	uploadSingleFileToAwsS3,
} = require('../utils/awsFileUpload');
const { getLeadById } = require('./leads.service');
const { getDealerById } = require('./dealer.service');

/**
 * Create a new appointment.
 * @param {Object} appointment - Appointment details.
 * @returns {Promise<Object>} - Created appointment data.
 */
const createAppointment = async (appointment, userId) => {
	const {
		title,
		description,
		start_date,
		end_date,
		start_time,
		end_time,
		meeting_place,
		dealer_id,
		lead_id,
		color,
		type,
	} = appointment;
	let uploadedFiles = [];

	if (appointment.files.length > 0) {
		const uploadPromises = await uploadFileToAwsS3(appointment.files);
		uploadedFiles = await Promise.all(uploadPromises);
	}

	// creating original appointment
	const appointmentData = await db.tbl_appointments.create({
		title,
		description: description ?? null,
		start_date,
		end_date,
		start_time,
		end_time,
		meeting_place,
		created_by: userId,
		dealer_id,
		lead_id,
		color,
		files: uploadedFiles,
		type,
	});

	return appointmentData;
};

/**
 * Get all appointments.
 * @returns {Promise<Object>} - Grouped appointments by month.
 */
const getAllAppointment = async () => {
	const appointments = await db.tbl_appointments.findAll({
		include: [
			{
				model: db.tbl_dealers,
				as: 'dealer',
				attributes: ['id', 'first_name', 'last_name'],
			},
			{
				model: db.tbl_wo_roles,
				as: 'appointment_created_by',
				attributes: ['role_name'],
			},
		],
		order: [['start_date', 'ASC']],
	});

	const monthNames = [
		'January',
		'February',
		'March',
		'April',
		'May',
		'June',
		'July',
		'August',
		'September',
		'October',
		'November',
		'December',
	];

	const groupedAppointments = monthNames.reduce((acc, month) => {
		acc[month] = [];
		return acc;
	}, {});

	appointments.forEach((appointment) => {
		if (!appointment.start_date || !appointment.start_time) {
			throw new ApiError(
				httpStatus.BAD_REQUEST,
				'Sart date ot Start time not found'
			);
		}

		const formattedDate =
			appointment.start_date instanceof Date
				? appointment.start_date.toISOString().split('T')[0]
				: appointment.start_date;

		const formattedTime = appointment.start_time;

		// Combine Date and Time in ISO format
		const startDateTime = new Date(`${formattedDate}T${formattedTime}`);

		// Validate Date
		if (Number.isNaN(startDateTime.getTime())) {
			throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid date format');
		}

		// Get month name
		const monthName = monthNames[startDateTime.getMonth()];

		if (!groupedAppointments[monthName]) {
			groupedAppointments[monthName] = []; // Initialize array if not exists
		}

		groupedAppointments[monthName].push({
			id: appointment.id,
			title: appointment.title,
			description: appointment.description,
			start_datetime: startDateTime, // Combined date and time
			meeting_place: appointment.meeting_place,
			type: appointment.type,
			file: appointment.file,
			dealer: appointment.dealer,
			created_by: appointment.appointment_created_by,
			specific_member_employee: appointment.specific_member_employee
				? appointment.specific_member_employee
				: [],
			reminder: appointment.reminder ? appointment.reminder : [],
			files: appointment.files
				? appointment.files.map((file) => ({
						id: file.id,
						file_url: file.file_url,
						file_name: file.file_name,
						file_size: file.file_size,
						file_type: file.file_type,
				  }))
				: [],
		});
	});

	return groupedAppointments;
};

/**
 * Get appointment by ID.
 * @param {number} appointmentId - Appointment ID.
 * @returns {Promise<Object>} - Appointment data.
 * @throws {ApiError} - If appointment not found.
 */
const getAppointmentById = async (appointmentId) => {
	const appointment = await db.tbl_appointments.findByPk(appointmentId);

	if (!appointment) {
		throw new ApiError(httpStatus.BAD_REQUEST, 'Appointment not found');
	}

	return appointment;
};

/**
 * Update an appointment by ID.
 * @param {number} appointmentId - Appointment ID.
 * @param {Object} appointment - Updated appointment data.
 * @returns {Promise<boolean>} - Returns true if the update is successful.
 */
const updateAppointmentById = async (appointmentId, appointment, userId) => {
	const { files } = appointment;

	const appointmentData = await getAppointmentById(appointmentId);

	const existingFiles = appointmentData.files || [];
	let updatedFiles = [];

	// Delete files that are missing in the request
	const deletionResults = await Promise.all(
		existingFiles.map(async (file) => {
			const stillExists = files.some((f) => f.id === file.id);
			if (!stillExists) {
				await deleteFromS3(file.file_key); // delete from AWS S3
				return null;
			}
			return file; // keep the file
		})
	);
	updatedFiles = deletionResults.filter(Boolean); // remove nulls

	// Upload new files
	const uploadResults = await Promise.all(
		files.map(async (file) => {
			if (!file.id && file.image) {
				const fileToUpload = { ...file, folder_name: 'winco' };
				return uploadSingleFileToAwsS3(fileToUpload); // No `await` needed
			}
			return null;
		})
	);

	updatedFiles = updatedFiles.concat(uploadResults.filter(Boolean));

	const updatedAppointment = {
		...appointment,
		files: updatedFiles,
		modified_by: userId,
	};

	await db.tbl_appointments.update(updatedAppointment, {
		where: { id: appointmentData.id },
	});

	return true;
};

/**
 * Update the reminder for an appointment.
 * @param {number} appointmentId - Appointment ID.
 * @param {Array<string>} reminder - Reminder types.
 * @returns {Promise<number>} - Number of updated rows.
 */
const updateAppointmentReminder = async (appointmentId, reminder) => {
	await getAppointmentById(appointmentId);

	return db.tbl_appointments.update(
		{ reminder },
		{
			where: { id: appointmentId },
		}
	);
};

/**
 * Delete an appointment by ID.
 * @param {number} appointmentId - Appointment ID.
 * @returns {Promise<boolean>} - Returns true if the appointment is deleted.
 */
const deleteAppointmentById = async (appointmentId, userId) => {
	await getAppointmentById(appointmentId);

	// let existingFiles = appointmentData.files || [];

	// if (existingFiles.length > 0) {
	//     for (const file of existingFiles) {

	//         await deleteFromS3(file.file_key); // delete from aws s3
	//     }
	// }

	await db.tbl_appointments.destroy({
		where: { id: appointmentId },
		individualHooks: true,
		deleted_by: userId,
	});

	return true;
};

const getAppointmentByUserId = async (userId, role) => {
	const whereCondition = {};

	if (role === 3) {
		await getDealerById(userId);
		whereCondition.dealer_id = userId;
	} else if (role === 4) {
		await getLeadById(userId);
		whereCondition.lead_id = userId;
	} else {
		throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid role provided');
	}

	const appointments = await db.tbl_appointments.findAll({
		where: whereCondition,
		include: [
			{
				model: db.tbl_dealers,
				as: 'dealer',
				attributes: ['id', 'first_name', 'last_name'],
			},
			{
				model: db.tbl_wo_roles,
				as: 'appointment_created_by',
				attributes: ['role_name'],
			},
		],
		order: [['start_date', 'ASC']],
	});

	const monthNames = [
		'January',
		'February',
		'March',
		'April',
		'May',
		'June',
		'July',
		'August',
		'September',
		'October',
		'November',
		'December',
	];

	const groupedAppointments = monthNames.reduce((acc, month) => {
		acc[month] = [];
		return acc;
	}, {});

	appointments.forEach((appointment) => {
		if (!appointment.start_date || !appointment.start_time) {
			throw new ApiError(
				httpStatus.BAD_REQUEST,
				'Sart date ot Start time not found'
			);
		}

		const formattedDate =
			appointment.start_date instanceof Date
				? appointment.start_date.toISOString().split('T')[0]
				: appointment.start_date;

		const formattedTime = appointment.start_time;

		// Combine Date and Time in ISO format
		const startDateTime = new Date(`${formattedDate}T${formattedTime}`);

		// Validate Date
		if (Number.isNaN(startDateTime.getTime())) {
			throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid date format');
		}

		// Get month name
		const monthName = monthNames[startDateTime.getMonth()];

		if (!groupedAppointments[monthName]) {
			groupedAppointments[monthName] = []; // Initialize array if not exists
		}

		groupedAppointments[monthName].push({
			id: appointment.id,
			title: appointment.title,
			description: appointment.description,
			start_datetime: startDateTime, // Combined date and time
			meeting_place: appointment.meeting_place,
			type: appointment.type,
			file: appointment.file,
			dealer: appointment.dealer,
			created_by: appointment.appointment_created_by,
			specific_member_employee: appointment.specific_member_employee
				? appointment.specific_member_employee
				: [],
			reminder: appointment.reminder ? appointment.reminder : [],
			files: appointment.files
				? appointment.files.map((file) => ({
						id: file.id,
						file_url: file.file_url,
						file_name: file.file_name,
						file_size: file.file_size,
						file_type: file.file_type,
				  }))
				: [],
		});
	});

	return groupedAppointments;
};

module.exports = {
	createAppointment,
	getAllAppointment,
	updateAppointmentById,
	getAppointmentById,
	updateAppointmentReminder,
	deleteAppointmentById,
	getAppointmentByUserId,
};
