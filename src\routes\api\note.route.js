const express = require('express');
const validate = require('../../middlewares/validate');
const { noteValidation } = require('../../validations');
const { noteController } = require('../../controllers');
const { grantAccess } = require('../../middlewares/validateAccessControl');
const { resources } = require('../../config/roles');

const router = express.Router();

// notes
router
	.route('/')
	.post(
		grantAccess('create', 'any', resources.NOTE),
		validate(noteValidation.createNote),
		noteController.createNote
	);
router
	.route('/:noteId')
	.get(
		grantAccess('read', 'any', resources.NOTE),
		validate(noteValidation.getNoteById),
		noteController.getNoteById
	)
	.patch(
		grantAccess('update', 'any', resources.NOTE),
		validate(noteValidation.updateNoteById),
		noteController.updateNoteById
	)
	.delete(
		grantAccess('delete', 'any', resources.NOTE),
		validate(noteValidation.getNoteById),
		noteController.deleteNoteById
	);

// dealer notes
router
	.route('/dealer/:dealerId')
	.get(
		grantAccess('read', 'any', resources.NOTE),
		validate(noteValidation.getNotesByDealerId),
		noteController.getNotesByDealerId
	);

// lead notes
router
	.route('/lead/:leadId')
	.get(
		grantAccess('read', 'any', resources.NOTE),
		validate(noteValidation.getNotesByLeadId),
		noteController.getNotesByLeadId
	);

module.exports = router;

/**
 * @swagger
 * components:
 *   schemas:
 *     Note:
 *       type: object
 *       properties:
 *         dealer_id:
 *           type: integer
 *           description: Unique ID of the dealer (either this or lead_id must be provided)
 *         lead_id:
 *           type: integer
 *           description: Unique ID of the lead (either this or dealer_id must be provided)
 *         title:
 *           type: string
 *           description: Title of the note
 *         description:
 *           type: string
 *           description: Description of the note (optional)
 *         labels:
 *           type: array
 *           items:
 *             type: string
 *           description: Array of labels (optional)
 *         files:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               id:
 *                 type: string
 *                 format: uuid
 *                 description: Unique ID of the file (optional)
 *               file_url:
 *                 type: string
 *                 format: uri
 *                 description: URL of the uploaded file (optional)
 *               document_name:
 *                 type: string
 *                 description: Name of the document
 *               image:
 *                 type: string
 *                 description: Base64 encoded image or file path
 *           example:
 *             - document_name: "appointment document"
 *               image: "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD..."
 */

/**
 * @swagger
 * /notes:
 *   post:
 *     summary: Create a new note for a dealer or lead
 *     description: >
 *       To create a note for a dealer, provide `dealer_id`.
 *       To create a note for a lead, provide `lead_id`.
 *       You can provide either one, but not both at the same time.
 *     tags: [Notes]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/Note'
 *           example:
 *             dealer_id: 1
 *             lead_id: null
 *             title: "Site Visit Summary"
 *             description: "Discussed curtain options and measurements"
 *             labels: ["Follow-up", "Urgent"]
 *             files:
 *               - document_name: "appointment document"
 *                 image: "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD..."
 *     responses:
 *       201:
 *         description: Note created successfully
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /notes/dealer/{dealerId}:
 *   get:
 *     summary: Get all notes for a specific dealer
 *     tags: [Notes]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: dealerId
 *         schema:
 *           type: integer
 *         required: true
 *         description: ID of the dealer
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of items per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term to filter notes
 *     responses:
 *       200:
 *         description: List of notes retrieved successfully
 *       400:
 *         description: Invalid dealer ID
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Dealer not found
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /notes/lead/{leadId}:
 *   get:
 *     summary: Get all notes for a specific lead
 *     tags: [Notes]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: leadId
 *         schema:
 *           type: integer
 *         required: true
 *         description: ID of the lead
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of items per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term to filter notes
 *     responses:
 *       200:
 *         description: List of notes retrieved successfully
 *       400:
 *         description: Invalid dealer ID
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Dealer not found
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /notes/{noteId}:
 *   get:
 *     summary: Get a specific note by ID
 *     tags: [Notes]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: noteId
 *         schema:
 *           type: integer
 *         required: true
 *         description: ID of the note
 *     responses:
 *       200:
 *         description: Note retrieved successfully
 *       400:
 *         description: Invalid note ID
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Note not found
 *       500:
 *         description: Internal server error
 *   delete:
 *     summary: Soft delete a specific note by ID
 *     tags: [Notes]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: noteId
 *         schema:
 *           type: integer
 *         required: true
 *         description: ID of the note to delete
 *     responses:
 *       200:
 *         description: Note deleted successfully
 *       400:
 *         description: Invalid note ID
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Note not found
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /notes/{noteId}:
 *   patch:
 *     summary: Update a specific note by ID
 *     description: >
 *       For the `files` array:
 *       - To **retain an existing file**, only pass its ID:
 *         `{ "id": "c23ab01e-15e5-49f9-8ea9-f5cb6bfe1baa" }`
 *       - To **add a new file**, include `document_name` and `image`:
 *         `{ "document_name": "appointment document", "image": "data:image/jpeg;base64,/9j/..." }`
 *     tags: [Notes]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: noteId
 *         schema:
 *           type: integer
 *         required: true
 *         description: ID of the note to update
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *                 description: Title of the note
 *               description:
 *                 type: string
 *                 description: Description of the note
 *               labels:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Labels to categorize the note
 *               files:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       format: uuid
 *                       description: ID of an existing file to retain
 *                     document_name:
 *                       type: string
 *                       description: Name of the new document (if uploading a new file)
 *                     image:
 *                       type: string
 *                       description: Base64-encoded image or file path (if uploading a new file)
 *           example:
 *             title: "test note title"
 *             description: "test note description"
 *             labels: ["followup"]
 *             files:
 *               - id: "c23ab01e-15e5-49f9-8ea9-f5cb6bfe1baa"
 *               - document_name: "new measurement"
 *                 image: "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD..."
 *     responses:
 *       200:
 *         description: Note updated successfully
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Note not found
 *       500:
 *         description: Internal server error
 */
