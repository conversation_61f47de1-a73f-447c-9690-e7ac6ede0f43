/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up(queryInterface, Sequelize) {
		const tableDescription = await queryInterface.describeTable('tbl_dealers');

		// Add 'address' column only if it doesn't exist
		if (!tableDescription.address) {
			await queryInterface.addColumn('tbl_dealers', 'address', {
				type: Sequelize.STRING,
				allowNull: true,
				defaultValue: null,
			});
		}

		if (!tableDescription.address_2) {
			await queryInterface.addColumn('tbl_dealers', 'address_2', {
				type: Sequelize.STRING,
				allowNull: true,
				defaultValue: null,
			});
		}
	},

	async down(queryInterface) {
		const tableDescription = await queryInterface.describeTable('tbl_dealers');

		// Remove 'address' column only if it exists
		if (tableDescription.address) {
			await queryInterface.removeColumn('tbl_dealers', 'address');
		}

		if (tableDescription.address_2) {
			await queryInterface.removeColumn('tbl_dealers', 'address_2');
		}
	},
};
