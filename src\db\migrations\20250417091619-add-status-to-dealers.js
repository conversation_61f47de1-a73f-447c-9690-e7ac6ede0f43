/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up(queryInterface, Sequelize) {
		const tableDescription = await queryInterface.describeTable('tbl_dealers');

		if (!tableDescription.status) {
			await queryInterface.addColumn('tbl_dealers', 'status', {
				type: Sequelize.INTEGER,
				allowNull: false,
				defaultValue: 0,
				comment:
					'0 = Pending, 1 = Active, 2 = Inactive, 3 = Rejected, 4 = Dealeted, 5 = Blocked',
			});
		}

		if (!tableDescription.status_updated_at) {
			await queryInterface.addColumn('tbl_dealers', 'status_updated_at', {
				type: Sequelize.DATE,
				allowNull: true,
			});
		}

		if (!tableDescription.status_updated_by) {
			await queryInterface.addColumn('tbl_dealers', 'status_updated_by', {
				type: Sequelize.INTEGER,
				allowNull: true,
			});
		}

		if (!tableDescription.status_reason) {
			await queryInterface.addColumn('tbl_dealers', 'status_reason', {
				type: Sequelize.TEXT,
				allowNull: true,
				comment: 'Reason for status change',
			});
		}
	},

	async down(queryInterface) {
		const tableDescription = await queryInterface.describeTable('tbl_dealers');

		const columnsToRemove = [
			'status',
			'status_updated_at',
			'status_updated_by',
			'status_reason',
		];

		await Promise.all(
			columnsToRemove.map(async (column) => {
				if (tableDescription[column]) {
					await queryInterface.removeColumn('tbl_dealers', column);
				}
			})
		);
	},
};
