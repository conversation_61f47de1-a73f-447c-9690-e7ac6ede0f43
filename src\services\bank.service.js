const httpStatus = require('http-status');
const { dealerService } = require('.');
const db = require('../db/models');
const ApiError = require('../utils/ApiError');

const getSoftDeletedBanks = async (dealer_id, account_number) => {
	const banks = await db.tbl_banks.findOne({
		where: { dealer_id, account_number },
		paranoid: false, // Include soft-deleted records
	});

	return banks;
};

/**
 * Get bank account details by account number.
 * @param {number} accountNo - Account number to search for.
 * @returns {Promise<Object|null>} - Bank account details if found, otherwise null.
 */
const getBankDetailsByAccountNo = async (accountNo) => {
	const bankAccount = await db.tbl_banks.findOne({
		where: {
			account_number: Buffer.from(String(accountNo), 'utf-8'),
		},
	});

	return bankAccount;
};

/**
 * Get bank details by bank ID.
 * @param {number} bankId - Bank ID to fetch.
 * @returns {Promise<Object>} - Bank details with account information.
 * @throws {ApiError} - Throws an error if bank account is not found.
 */
const getBankById = async (bankId) => {
	const bank = await db.tbl_banks.findByPk(bankId);

	if (!bank) {
		throw new ApiError(httpStatus.BAD_REQUEST, 'Bank account not found');
	}

	return {
		id: bank.id,
		dealer_id: bank.dealer_id,
		account_name: bank.account_name.toString(),
		bank_name: bank.bank_name.toString(),
		account_number: bank.account_number.toString(),
		ifsc: bank.ifsc.toString(),
		branch_name: bank.branch_name.toString(),
	};
};

/**
 * Update a bank account by ID.
 * @param {number} bankId - Bank ID to update.
 * @param {Object} bankDetails - Bank details to update:
 *   - {number} dealer_id
 *   - {string} account_name
 *   - {string} bank_name
 *   - {number} account_number
 *   - {string} ifsc
 *   - {string} branch_name
 * @returns {Promise<boolean>} - Returns true when successfully updated.
 * @throws {ApiError} - Throws error if account already exists.
 */
const updateBankById = async (bankId, updateReq, userId) => {
	const {
		dealer_id,
		account_name,
		bank_name,
		account_number,
		ifsc,
		branch_name,
	} = updateReq;

	await dealerService.getDealerById(dealer_id);

	const bank = await getBankById(bankId);

	if (account_number.toString() !== bank.account_number) {
		const checkExistingAccountNo = await getBankDetailsByAccountNo(
			account_number
		);

		if (checkExistingAccountNo) {
			throw new ApiError(
				httpStatus.BAD_REQUEST,
				'Bank account already exists with this account number'
			);
		}
	}

	const updateBody = {
		dealer_id,
		account_name: Buffer.from(account_name, 'utf-8'),
		bank_name: Buffer.from(bank_name, 'utf-8'),
		account_number: Buffer.from(String(account_number), 'utf-8'),
		ifsc: Buffer.from(ifsc, 'utf-8'),
		branch_name: Buffer.from(branch_name, 'utf-8'),
		modified_by: userId,
	};

	await db.tbl_banks.update(updateBody, {
		where: {
			id: bankId,
		},
	});

	return true;
};

/**
 * Create a new bank account for a dealer.
 * @param {number} dealerId - Dealer's ID.
 * @param {Object} bankDetails - Bank details including:
 *   - {string} account_name
 *   - {string} bank_name
 *   - {number} account_number
 *   - {string} ifsc
 *   - {string} branch_name
 * @returns {Promise<boolean>} - Returns true when successfully created.
 * @throws {ApiError} - Throws error if account already exists.
 */

const createDealerBank = async (dealerId, bankDetails, userId) => {
	const {
		dealer_id,
		account_name,
		bank_name,
		account_number,
		ifsc,
		branch_name,
	} = bankDetails;

	// get soft deleted bank
	const softDeletedBank = await getSoftDeletedBanks(dealer_id, account_number);

	if (softDeletedBank) {
		const existingAccount = await getBankDetailsByAccountNo(account_number);

		if (existingAccount) {
			throw new ApiError(
				httpStatus.BAD_REQUEST,
				'Bank account already exists with this account number'
			);
		}

		// resore the deleted data and update it
		await softDeletedBank.restore({ hooks: true, modified_by: userId });

		const bank = await updateBankById(softDeletedBank.id, bankDetails, userId);
		return bank;
	}
	const existingAccount = await getBankDetailsByAccountNo(account_number);

	if (existingAccount) {
		throw new ApiError(
			httpStatus.BAD_REQUEST,
			'Bank account already exists with this account number'
		);
	}

	await dealerService.getDealerById(dealerId);

	const bank = await db.tbl_banks.create({
		dealer_id,
		account_name: Buffer.from(account_name, 'utf-8'),
		bank_name: Buffer.from(bank_name, 'utf-8'),
		account_number: Buffer.from(String(account_number), 'utf-8'),
		ifsc: Buffer.from(ifsc, 'utf-8'),
		branch_name: Buffer.from(branch_name, 'utf-8'),
		created_by: userId,
	});

	return bank;
};

/**
 * Get all bank details with pagination.
 * @param {number} page - The page number for pagination.
 * @param {number} limit - The number of items per page.
 * @returns {Promise<Object>} - A promise that resolves with the bank details and pagination info.
 */
const getAllBankDetails = async (page, limit) => {
	// eslint-disable-next-line no-param-reassign
	page = parseInt(page, 10) || 1;

	// eslint-disable-next-line no-param-reassign
	limit = parseInt(limit, 10) || 10;

	const offset = (page - 1) * limit;

	const { count, rows } = await db.tbl_banks.findAndCountAll({
		order: [['created_at', 'DESC']],
		limit,
		offset,
		include: [
			{
				model: db.tbl_dealers,
				as: 'dealer',
				attributes: ['id', 'first_name', 'last_name'],
			},
		],
	});

	const totalPages = Math.ceil(count / limit);
	const nextPage = page < totalPages ? page + 1 : null;
	const prevPage = page > 1 ? page - 1 : null;

	const bankDetails = rows.map((bank) => ({
		id: bank.id,
		dealer_id: bank.dealer,
		account_name: bank.account_name.toString(),
		bank_name: bank.bank_name.toString(),
		account_number: bank.account_number.toString(),
		ifsc: bank.ifsc.toString(),
		branch_name: bank.branch_name.toString(),
		created_at: bank.created_at,
	}));

	return {
		bankDetails,
		pagination: {
			totalRows: count,
			filteredRows: rows.length,
			currentPage: page,
			totalPages,
			nextPage,
			prevPage,
			hasNextPage: page < totalPages,
			hasPrevPage: page > 1,
		},
	};
};

/**
 * Delete a bank by its ID.
 * @param {number} bankId - The ID of the bank to be deleted.
 * @returns {Promise<boolean>} - A promise that resolves to true when the bank is successfully deleted.
 * @throws {ApiError} - Throws an error if the bank account does not exist.
 */
const deleteBankById = async (bankId, deletedBy) => {
	await getBankById(bankId);

	await db.tbl_banks.destroy({
		where: {
			id: bankId,
		},
		individualHooks: true,
		deleted_by: deletedBy,
	});

	return true;
};

const getBankByDealerId = async (dealerId) => {
	const dealerBank = await db.tbl_banks.findOne({
		where: {
			dealer_id: dealerId,
		},
		raw: true,
	});

	return dealerBank;
};

module.exports = {
	createDealerBank,
	getAllBankDetails,
	getBankDetailsByAccountNo,
	deleteBankById,
	getBankById,
	updateBankById,
	getBankByDealerId,
	getSoftDeletedBanks,
};
