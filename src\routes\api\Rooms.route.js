const express = require('express');

const router = express.Router();
const {
	addRoom,
	getMyRooms,
	getRoomDetails,
	updateRoom,
	deleteRoom,
} = require('../../controllers/room.controller');

const {
	validateRoom,
	updateRoomDetail,
} = require('../../validations/room.validation');
const authMiddleware = require('../../middlewares/authMiddleware');
const validate = require('../../middlewares/validate');

router.post('/add-room', authMiddleware, validateRoom, addRoom);
router.get('/my-rooms', authMiddleware, getMyRooms);

/**
 * @swagger
 * tags:
 *   name: Rooms
 *   description: Room Management API
 */

/**
 * @swagger
 * /rooms/add-room:
 *   post:
 *     summary: Add a new room with windows
 *     description: |
 *       Creates a new room with multiple windows for a specific user. The API handles:
 *
 *       1. Image Processing:
 *          - Accepts base64 encoded images for each window
 *          - Automatically uploads images to AWS S3
 *          - Generates unique S3 keys for each image
 *
 *       2. Data Storage:
 *          - Creates room record in PostgreSQL
 *          - Creates associated window records
 *          - Links windows to the room
 *
 *       3. Security:
 *          - Validates user authentication
 *          - Ensures user can only create rooms for themselves
 *          - Validates image formats and sizes
 *
 *       **Note:** The user_id in the request must match the authenticated user's ID.
 *     tags: [Rooms]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - user_id
 *               - room_name
 *               - windows
 *             properties:
 *               user_id:
 *                 type: integer
 *                 description: |
 *                   The ID of the authenticated user creating the room.
 *                   Must match the ID from the authentication token.
 *                 example: 1
 *               room_name:
 *                 type: string
 *                 description: |
 *                   Name of the room (e.g., Living Room, Kitchen, etc.).
 *                   Should be unique for the user.
 *                 minLength: 1
 *                 maxLength: 100
 *                 example: "Master Bedroom"
 *               windows:
 *                 type: array
 *                 description: |
 *                   List of windows in the room. Each window requires:
 *                   - A unique name within the room
 *                   - Window dimensions in inches
 *                   - A base64 encoded image
 *                 minItems: 1
 *                 items:
 *                   type: object
 *                   required:
 *                     - window_name
 *                     - image_base64
 *                     - width
 *                     - height
 *                   properties:
 *                     window_name:
 *                       type: string
 *                       description: |
 *                         Unique name for the window within the room.
 *                         Used for identification and reference.
 *                       minLength: 1
 *                       maxLength: 100
 *                       example: "North Window"
 *                     image_base64:
 *                       type: string
 *                       format: base64
 *                       description: |
 *                         Base64 encoded image data.
 *                         Supported formats: JPG, PNG
 *                         Max file size: 5MB
 *                       example: "data:image/jpeg;base64,/9j/4AAQSkZJRg..."
 *                     width:
 *                       type: number
 *                       description: |
 *                         Width of the window in inches.
 *                         Must be a positive number.
 *                       minimum: 1
 *                       example: 36.5
 *                     height:
 *                       type: number
 *                       description: |
 *                         Height of the window in inches.
 *                         Must be a positive number.
 *                       minimum: 1
 *                       example: 48.75
 *     responses:
 *       "201":
 *         description: Room and windows added successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Room and windows created successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     room_id:
 *                       type: integer
 *                       description: Unique identifier for the created room
 *                       example: 1
 *                     room_name:
 *                       type: string
 *                       description: Name of the created room
 *                       example: "Master Bedroom"
 *                     windows:
 *                       type: array
 *                       description: List of created windows with their details
 *                       items:
 *                         type: object
 *                         properties:
 *                           window_id:
 *                             type: integer
 *                             description: Unique identifier for the window
 *                             example: 1
 *                           window_name:
 *                             type: string
 *                             description: Name of the window
 *                             example: "North Window"
 *                           image_url:
 *                             type: string
 *                             format: uri
 *                             description: Public URL for the uploaded image
 *                             example: "https://s3.amazonaws.com/bucket/image.jpg"
 *                           width:
 *                             type: number
 *                             description: Width of the window in inches
 *                             example: 36.5
 *                           height:
 *                             type: number
 *                             description: Height of the window in inches
 *                             example: 48.75
 *       "400":
 *         description: Bad request due to validation error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Validation error"
 *                 errors:
 *                   type: array
 *                   items:
 *                     type: string
 *                   example: [
 *                     "Room name is required",
 *                     "At least one window is required",
 *                     "Invalid image format",
 *                     "Window dimensions must be positive numbers"
 *                   ]
 *       "401":
 *         description: Unauthorized - Missing or invalid authentication token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Authentication required"
 *       "403":
 *         description: Forbidden - User ID mismatch
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Invalid token: user ID does not match"
 *       "500":
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Internal server error"
 *                 error:
 *                   type: string
 *                   description: Technical error details (only in development)
 */
/**
 * @swagger
 * /rooms/my-rooms:
 *   get:
 *     summary: Get rooms for the authenticated user
 *     description: |
 *       Retrieves all rooms and their associated windows for the authenticated user.
 *
 *       Features:
 *       1. Authentication:
 *          - Requires valid bearer token
 *          - Automatically filters rooms based on authenticated user
 *
 *       2. Data Retrieval:
 *          - Returns all rooms owned by the user
 *          - Includes detailed window information for each room
 *          - Provides image URLs for window images
 *
 *       3. Access Control:
 *          - Users can only access their own rooms
 *     tags: [Rooms]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of rooms retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Rooms fetched successfully"
 *                 data:
 *                   type: array
 *                   description: List of rooms with their windows
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                         description: Unique identifier for the room
 *                         example: 1
 *                       user_id:
 *                         type: integer
 *                         description: ID of the room owner
 *                         example: 123
 *                       room_name:
 *                         type: string
 *                         description: Name of the room
 *                         example: "Master Bedroom"
 *                       created_date:
 *                         type: string
 *                         format: date-time
 *                         description: Room creation timestamp
 *                         example: "2023-01-01T12:00:00Z"
 *                       windows:
 *                         type: array
 *                         description: List of windows in the room
 *                         items:
 *                           type: object
 *                           properties:
 *                             id:
 *                               type: integer
 *                               description: Unique identifier for the window
 *                               example: 1
 *                             room_id:
 *                               type: integer
 *                               description: ID of the parent room
 *                               example: 1
 *                             window_name:
 *                               type: string
 *                               description: Name of the window
 *                               example: "North Window"
 *                             width:
 *                               type: number
 *                               description: Width in inches
 *                               example: 36.5
 *                             height:
 *                               type: number
 *                               description: Height in inches
 *                               example: 48.75
 *                             image_url:
 *                               type: string
 *                               format: uri
 *                               description: URL to window image
 *                               example: "https://s3.amazonaws.com/bucket/image.jpg"
 *                             s3_key:
 *                               type: string
 *                               description: S3 storage key for the image
 *                               example: "rooms/123/windows/1.jpg"
 *       401:
 *         description: Unauthorized - Invalid or missing token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Authentication required"
 *       403:
 *         description: Forbidden - User doesn't have required permissions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Access denied"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Internal server error"
 */
/**
 * @swagger
 * /rooms/{id}:
 *   get:
 *     summary: Get detailed information about a specific room
 *     description: |
 *       Retrieves comprehensive information about a specific room and its windows.
 *
 *       Features:
 *       1. Authentication & Authorization:
 *          - Requires valid bearer token
 *          - Validates user's access rights to the room
 *          - Supports role-based access control
 *
 *       2. Data Retrieval:
 *          - Detailed room information
 *          - Complete window specifications
 *          - Image URLs and metadata
 *
 *       3. Access Control:
 *          - Users can only access their own rooms
 *          - Admins can access any room
 *          - Validates room ownership
 *     tags: [Rooms]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Numeric ID of the room to retrieve
 *         example: 1
 *     responses:
 *       200:
 *         description: Room details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Room detail fetch successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       description: Room's unique identifier
 *                       example: 1
 *                     user_id:
 *                       type: integer
 *                       description: ID of room owner
 *                       example: 123
 *                     room_name:
 *                       type: string
 *                       description: Name of the room
 *                       example: "Master Bedroom"
 *                     created_date:
 *                       type: string
 *                       format: date-time
 *                       description: Room creation timestamp
 *                       example: "2023-01-01T12:00:00Z"
 *                     windows:
 *                       type: array
 *                       description: Detailed list of windows in the room
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                             description: Window's unique identifier
 *                             example: 1
 *                           room_id:
 *                             type: integer
 *                             description: ID of parent room
 *                             example: 1
 *                           window_name:
 *                             type: string
 *                             description: Name of the window
 *                             example: "North Window"
 *                           width:
 *                             type: number
 *                             description: Width in inches
 *                             example: 36.5
 *                           height:
 *                             type: number
 *                             description: Height in inches
 *                             example: 48.75
 *                           image_url:
 *                             type: string
 *                             format: uri
 *                             description: URL to window image
 *                             example: "https://s3.amazonaws.com/bucket/image.jpg"
 *                           s3_key:
 *                             type: string
 *                             description: S3 storage key for image
 *                             example: "rooms/123/windows/1.jpg"
 *       401:
 *         description: Unauthorized - Invalid or missing token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Authentication required"
 *       403:
 *         description: Forbidden - User doesn't have access to this room
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Access denied"
 *       404:
 *         description: Room not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Room not found"
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Internal server error"
 */

router.get('/:id', authMiddleware, getRoomDetails);

/**
 * @swagger
 * /rooms/update-room/{roomId}:
 *   patch:
 *     summary: Update an existing room and its windows
 *     description: |
 *       Updates a room and its windows. Supports:
 *       - Updating room name only
 *       - Updating existing windows
 *       - Adding new windows
 *       - Removing existing windows
 *       - Combination of above operations
 *     tags: [Rooms]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: roomId
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the room to update
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               room_name:
 *                 type: string
 *                 description: Updated name for the room
 *               windows:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                     window_name:
 *                       type: string
 *                     width:
 *                       type: number
 *                     height:
 *                       type: number
 *                     image_base64:
 *                       type: string
 *               windows_to_delete:
 *                 type: array
 *                 items:
 *                   type: integer
 *           examples:
 *             updateRoomNameOnly:
 *               summary: Update room name only
 *               value:
 *                 room_name: "Updated Master Bedroom"
 *             updateExistingWindow:
 *               summary: Update existing window (without image)
 *               value:
 *                 room_name: "Master Bedroom"
 *                 windows: [
 *                   {
 *                     id: 1,
 *                     window_name: "North Window Updated",
 *                     width: 42.5,
 *                     height: 52.75
 *                   }
 *                 ]
 *             updateExistingWindowWithImage:
 *               summary: Update existing window (including image)
 *               value:
 *                 room_name: "Master Bedroom"
 *                 windows: [
 *                   {
 *                     id: 1,
 *                     window_name: "North Window Updated",
 *                     width: 42.5,
 *                     height: 52.75,
 *                     image_base64: "data:image/jpeg;base64,/9j/4AAQSkZJRg..."
 *                   }
 *                 ]
 *             addNewWindow:
 *               summary: Add a new window to room
 *               value:
 *                 room_name: "Master Bedroom"
 *                 windows: [
 *                   {
 *                     window_name: "East Window",
 *                     width: 36.5,
 *                     height: 48.75,
 *                     image_base64: "data:image/jpeg;base64,/9j/4AAQSkZJRg..."
 *                   }
 *                 ]
 *             deleteWindows:
 *               summary: Delete existing windows
 *               value:
 *                 room_name: "Master Bedroom"
 *                 windows_to_delete: [2, 3]
 *             complexUpdate:
 *               summary: Complex update (multiple operations)
 *               value:
 *                 room_name: "Updated Master Bedroom"
 *                 windows: [
 *                   {
 *                     id: 1,
 *                     window_name: "North Window Modified",
 *                     width: 40.5,
 *                     height: 50.75
 *                   },
 *                   {
 *                     window_name: "New East Window",
 *                     width: 36.5,
 *                     height: 48.75,
 *                     image_base64: "data:image/jpeg;base64,/9j/4AAQSkZJRg..."
 *                   }
 *                 ]
 *                 windows_to_delete: [2, 3]
 *     responses:
 *       "200":
 *         description: Room updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Room updated successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                     room_name:
 *                       type: string
 *                     windows:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                           window_name:
 *                             type: string
 *                           width:
 *                             type: number
 *                           height:
 *                             type: number
 *                           image_url:
 *                             type: string
 *       "400":
 *         description: Validation error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Validation error"
 *                 errors:
 *                   type: array
 *                   items:
 *                     type: string
 *                   example: [
 *                     "Room name is required",
 *                     "Window dimensions must be positive numbers",
 *                     "Invalid image format"
 *                   ]
 *       "401":
 *         description: Unauthorized - Missing or invalid token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Authentication required"
 *       "403":
 *         description: Forbidden - User doesn't own the room
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "You do not have permission to update this room"
 *       "404":
 *         description: Room not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Room not found"
 *       "500":
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Internal server error"
 */

router.patch(
	'/update-room/:roomId',
	authMiddleware,
	validate(updateRoomDetail),
	updateRoom
);
router.delete('/delete/:roomId', authMiddleware, deleteRoom);
module.exports = router;
/**
 * @swagger
 * /rooms/delete/{roomId}:
 *   delete:
 *     summary: Soft delete a room
 *     description: |
 *       Performs a soft delete of a room while preserving its associated windows.
 *
 *       Features:
 *       1. Authentication & Authorization:
 *          - Requires valid bearer token
 *          - Validates user owns the room
 *          - Prevents unauthorized deletions
 *
 *       2. Deletion Process:
 *          - Performs soft delete on room only (sets is_delete flag to true)
 *          - Records deletion metadata (deleted_by, deleted_at)
 *          - Maintains data integrity
 *
 *       3. Data Preservation:
 *          - Keeps all associated windows intact
 *          - Maintains window relationships
 *          - Preserves room-window associations
 *
 *       **Important Notes:**
 *       - This is a soft delete operation
 *       - Associated windows remain unchanged
 *       - Room data remains in database but marked as deleted
 *       - Room name becomes available for reuse
 *     tags: [Rooms]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: roomId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Numeric ID of the room to delete
 *         example: 123
 *     responses:
 *       "200":
 *         description: Room successfully marked as deleted
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                   description: Indicates successful deletion
 *                 message:
 *                   type: string
 *                   example: "Room marked as deleted successfully"
 *                   description: Success message
 *       "400":
 *         description: Bad request - Invalid room ID
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Invalid room ID provided"
 *       "401":
 *         description: Unauthorized - Missing or invalid authentication token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Authentication required"
 *       "403":
 *         description: Forbidden - User doesn't have permission to delete this room
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Room not found or you do not have permission to delete it"
 *       "404":
 *         description: Not Found - Room doesn't exist or is already deleted
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Room not found or you do not have permission to delete it"
 *       "500":
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "An unexpected error occurred while deleting the room"
 */
