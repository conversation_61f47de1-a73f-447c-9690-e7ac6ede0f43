const Joi = require('@hapi/joi');

const createDealerBank = {
	body: Joi.object().keys({
		dealer_id: Joi.number().required(),
		account_name: Joi.string().required(),
		bank_name: Joi.string().required(),
		account_number: Joi.number().required(),
		ifsc: Joi.string().required(),
		branch_name: Joi.string().required(),
	}),
};

const deleteBankById = {
	params: Joi.object().keys({
		bankId: Joi.number().integer().positive().required(),
	}),
};

const updateBankById = {
	params: Joi.object().keys({
		bankId: Joi.number().integer().positive().required(),
	}),
	body: Joi.object().keys({
		dealer_id: Joi.number().required(),
		account_name: Joi.string().required(),
		bank_name: Joi.string().required(),
		account_number: Joi.number().required(),
		ifsc: Joi.string().required(),
		branch_name: Joi.string().required(),
	}),
};

module.exports = {
	createDealerBank,
	deleteBankById,
	updateBankById,
};
