const stream = require('stream');
const httpStatus = require('http-status');
const { v4: uuidv4 } = require('uuid');
const { PutObjectCommand, DeleteObjectCommand } = require('@aws-sdk/client-s3');
const path = require('path');
const config = require('../config/config');
const ApiError = require('./ApiError');
const s3Client = require('../config/aws-config');

// upload base64 files in array
const uploadFileToAwsS3 = async (files) => {
	const allowedTypes = new Set(['image/jpeg', 'image/png', 'application/pdf']);
	const uploadPromises = files.map(async (file) => {
		const { image, document_name } = file;

		if (!image) throw new Error('Each file must have a base64 string');

		// Extract file type and base64 data
		const match = image.match(/^data:(.*?);base64,(.*)$/);
		if (!match) throw new Error('Invalid base64 format');

		// eslint-disable-next-line no-unused-vars
		const [_, file_type, file_data] = match;
		if (!allowedTypes.has(file_type))
			throw new Error('Only JPG, PNG, and PDF files are allowed');

		// Generate unique file name
		const extension = file_type.split('/')[1];

		const file_name = `${document_name.replace(
			/\s+/g,
			''
		)}_${Date.now()}_${Math.random()
			.toString(36)
			.substring(7)}.${extension.replace(/\s+/g, '')}`;

		const fileKey = `winco/${file_name}`;

		// Convert base64 to buffer and stream it to S3
		const buffer = Buffer.from(file_data, 'base64');
		const readableStream = new stream.PassThrough();
		readableStream.end(buffer);

		try {
			const command = new PutObjectCommand({
				Bucket: config.awsS3.AWS_BUCKET_NAME,
				Key: fileKey,
				Body: buffer,
				ContentType: file_type,
			});

			await s3Client.send(command);

			return {
				id: uuidv4(),
				file_name: document_name,
				// file_url: uploadResult.Location,
				file_url: `https://${config.awsS3.AWS_BUCKET_NAME}.s3.${config.awsS3.region}.amazonaws.com/${fileKey}`,
				file_type,
				file_size: buffer.length,
				added_by: 1,
				file_key: fileKey,
			};
		} catch (error) {
			throw new ApiError(
				httpStatus.BAD_REQUEST,
				`File upload failed-- ${error}`
			);
		}
	});

	return uploadPromises;
};

// upload single base64 file
const uploadSingleFileToAwsS3 = async (file) => {
	const allowedTypes = new Set(['image/jpeg', 'image/png', 'application/pdf']);

	const { image, document_name, folder_name } = file;

	if (!image) throw new Error('Each file must have a base64 string');

	// Extract file type and base64 data
	const match = image.match(/^data:(.*?);base64,(.*)$/);
	if (!match) throw new Error('Invalid base64 format');

	// eslint-disable-next-line no-unused-vars
	const [_, file_type, file_data] = match;

	if (!allowedTypes.has(file_type))
		throw new Error('Only JPG, PNG, and PDF files are allowed');

	// Generate unique file name
	const extension = file_type.split('/')[1];
	const file_name = `${document_name.replace(
		/\s+/g,
		''
	)}_${Date.now()}_${Math.random()
		.toString(36)
		.substring(7)}.${extension.replace(/\s+/g, '')}`;
	const fileKey = `${folder_name}/${file_name}`;

	// Convert base64 to buffer and stream it to S3
	const buffer = Buffer.from(file_data, 'base64');
	const readableStream = new stream.PassThrough();
	readableStream.end(buffer);

	try {
		const command = new PutObjectCommand({
			Bucket: config.awsS3.AWS_BUCKET_NAME,
			Key: fileKey,
			Body: buffer,
			ContentType: file_type,
		});

		await s3Client.send(command);

		return {
			id: uuidv4(),
			file_name: document_name,
			file_url: `https://${config.awsS3.AWS_BUCKET_NAME}.s3.${config.awsS3.region}.amazonaws.com/${fileKey}`,
			file_type,
			file_size: buffer.length,
			added_by: 1,
			file_key: fileKey,
		};
	} catch (error) {
		throw new ApiError(httpStatus.BAD_REQUEST, `File upload failed-- ${error}`);
	}
};

const deleteFromS3 = async (fileKey) => {
	const params = {
		Bucket: config.awsS3.AWS_BUCKET_NAME,
		Key: fileKey,
	};

	try {
		const command = new DeleteObjectCommand(params);
		await s3Client.send(command);
	} catch (error) {
		throw new ApiError(
			httpStatus.BAD_REQUEST,
			`3 file deletion failed --- ${error}`
		);
	}
};

// upload  multipart/form-data file
const uploadDirectFileToS3 = async (
	fileBuffer,
	originalName,
	mimeType,
	folder,
	fileName
) => {
	// eslint-disable-next-line no-param-reassign
	folder = folder || 'common';
	const fileExt = path.extname(originalName);
	const fileKey = `${folder}/${fileName}${fileExt}`;

	const command = new PutObjectCommand({
		Bucket: config.awsS3.AWS_BUCKET_NAME,
		Key: fileKey,
		Body: fileBuffer,
		ContentType: mimeType,
	});

	await s3Client.send(command);

	return {
		fileKey,
		fileUrl: `https://${config.awsS3.AWS_BUCKET_NAME}.s3.${config.awsS3.region}.amazonaws.com/${fileKey}`,
	};
};

module.exports = {
	uploadFileToAwsS3,
	uploadSingleFileToAwsS3,
	deleteFromS3,
	uploadDirectFileToS3,
};
