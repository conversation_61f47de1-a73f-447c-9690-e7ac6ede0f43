'use_strict';

module.exports = (sequelize, DataTypes) => {
	const socialLinks = sequelize.define(
		'tbl_social_links',
		{
			id: {
				type: DataTypes.INTEGER,
				autoIncrement: true,
				primaryKey: true,
				allowNull: false,
			},
			facebook: {
				type: DataTypes.STRING(100),
				allowNull: true,
			},
			twitter: {
				type: DataTypes.STRING(100),
				allowNull: true,
			},
			linkedin: {
				type: DataTypes.STRING(100),
				allowNull: true,
			},
			instagram: {
				type: DataTypes.STRING(100),
				allowNull: true,
			},
			youtube: {
				type: DataTypes.STRING(100),
				allowNull: true,
			},
			pinterest: {
				type: DataTypes.STRING(100),
				allowNull: true,
			},
			whatsapp: {
				type: DataTypes.STRING(100),
				allowNull: true,
			},
			tiktok: {
				type: DataTypes.STRING(100),
				allowNull: true,
			},
			user_id: {
				type: DataTypes.INTEGER,
				allowNull: false,
			},
			created_by: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			created_date: {
				type: DataTypes.DATE,
				allowNull: false,
				defaultValue: DataTypes.NOW,
			},
			updated_by: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			updated_date: {
				type: DataTypes.DATE,
				allowNull: true,
			},
			deleted: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: false, // 0 means not deleted
				comment: 'true = deleted, false = not deleted', // Add comment
			},
			deleted_by: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			deleted_date: {
				type: DataTypes.DATE,
				allowNull: true,
			},
		},
		{
			tableName: 'tbl_social_links',
			timestamps: true,
			paranoid: true,
			createdAt: 'created_date',
			updatedAt: 'updated_date',
			deletedAt: 'deleted_date',
			underscored: true, // Using custom created_date and updated_date
		}
	);

	socialLinks.associate = (models) => {
		socialLinks.belongsTo(models.tbl_users, {
			foreignKey: 'user_id',
			as: 'user',
		});
	};
	return socialLinks;
};
