const bcrypt = require('bcryptjs');
/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up(queryInterface) {
		const hashedPassword = await bcrypt.hash('123456', 10);
		return queryInterface.bulkInsert('tbl_users', [
			{
				first_name: 'Super',
				last_name: 'Admin',
				mobile_no: null,
				otp: null,
				unique_id: 0,
				profile_image: null,
				profile_image_path: null,
				primary_email: '<EMAIL>',
				password: hashedPassword, // ✅ Encrypted Password
				token: null,
				device_token: null,
				wo_role_id: 1,
				home_owner_id: null,
				dealer_id: null,
				two_step_verification: false,
				is_contact: true,
				login_access: true,
				is_active: true,
				// created_date : new Date(),
				created_by: null,
				// modified_date : null,
				modified_by: null,
				is_deleted: true,
				deleted_by: null,
				deleted_date: null,
				dealer_role_id: 0,
			},
		]);
	},

	async down(queryInterface) {
		return queryInterface.bulkDelete('tbl_users', {
			primary_email: '<EMAIL>',
		});
	},
};
