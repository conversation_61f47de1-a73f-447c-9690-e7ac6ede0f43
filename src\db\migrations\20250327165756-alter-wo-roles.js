/** @type {import('sequelize-cli').Migration} */
module.exports = {
	up: async (queryInterface, Sequelize) => {
		const tableDescription = await queryInterface.describeTable('tbl_wo_roles');

		// Check if 'role_name' exists and is ARRAY, then change it
		if (
			tableDescription.role_name &&
			tableDescription.role_name.type === 'ARRAY'
		) {
			await queryInterface.changeColumn('tbl_wo_roles', 'role_name', {
				type: Sequelize.STRING(255),
				allowNull: false,
			});
		}

		// Check if 'slug' exists and is ARRAY, then change it
		if (tableDescription.slug && tableDescription.slug.type === 'ARRAY') {
			await queryInterface.changeColumn('tbl_wo_roles', 'slug', {
				type: Sequelize.STRING(255),
				allowNull: false,
			});
		}
	},

	down: async (queryInterface, Sequelize) => {
		const tableDescription = await queryInterface.describeTable('tbl_wo_roles');

		if (
			tableDescription.role_name &&
			tableDescription.role_name.type === 'STRING'
		) {
			await queryInterface.changeColumn('tbl_wo_roles', 'role_name', {
				type: Sequelize.ARRAY(Sequelize.STRING(255)),
				allowNull: false,
			});
		}

		if (tableDescription.slug && tableDescription.slug.type === 'STRING') {
			await queryInterface.changeColumn('tbl_wo_roles', 'slug', {
				type: Sequelize.ARRAY(Sequelize.STRING(255)),
				allowNull: false,
			});
		}
	},
};
