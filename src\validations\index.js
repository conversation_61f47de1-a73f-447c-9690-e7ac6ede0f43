module.exports.authValidation = require('./auth.validation');
module.exports.userValidation = require('./user.validation');
module.exports.roleValidation = require('./role.validation');
module.exports.homeownerValidation = require('./homeowner.validation');
module.exports.adminValidation = require('./admin.validation');
module.exports.appointmentValidation = require('./appointment.validation');

module.exports.leadStatusValidation = require('./lead_status.validation');
module.exports.leadTypesValidation = require('./lead_types.validation');
module.exports.leadsValidation = require('./lead_status.validation');
module.exports.validateLeadLostStatus = require('./lead_lost_status.validation');
module.exports.dealerValidation = require('./dealer.validation');
module.exports.bankValidation = require('./bank.validation');
module.exports.noteValidation = require('./note.validation');
module.exports.commonValidation = require('./common.validation');

module.exports.validateNotesLabel = require('./notes_label.validation');
module.exports.validateLeadTypes = require('./lead_types.validation');
module.exports.validateLeadStatus = require('./lead_status.validation');
module.exports.validateLeadSource = require('./lead_source.validation');
module.exports.validateLeads = require('./leads.validation');
module.exports.woRoutesValidations = require('./wo_roles.validation');
module.exports.employeeValidations = require('./employees.validation');
