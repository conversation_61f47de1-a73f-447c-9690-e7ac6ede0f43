const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const ApiSuccess = require('../utils/ApiSuccess');
const { bankService } = require('../services');
const ApiError = require('../utils/ApiError');
const RESPONSE = require('../utils/ApiResponseMsg');

/**
 * Create a bank account for a dealer.
 * @param {Object} bankDetails - Bank details including:
 *   - {number} dealer_id
 *   - {string} account_name
 *   - {string} bank_name
 *   - {string} account_number
 *   - {string} ifsc
 *   - {string} branch_name
 * @returns {Promise<void>}
 */
const createDealerBank = catchAsync(async (req, res) => {
	const { userId } = req.user;
	const { dealer_id, account_name } = req.body;
	const existingBankAccount = await bankService.getBankByDealerId(dealer_id);

	if (existingBankAccount) {
		throw new ApiError(
			httpStatus.BAD_REQUEST,
			'Bank account for this dealer already exists'
		);
	}

	const bank = await bankService.createDealerBank(dealer_id, req.body, userId);

	const bankRes = {
		id: bank.id,
		account_name,
		created_at: bank.created_at,
	};

	return new ApiSuccess(
		res,
		httpStatus.OK,
		RESPONSE.CREATE_SUCCESS('bank'),
		bankRes
	);
});

/**
 * Get all bank details with pagination.
 * @param {number} page - Page number for pagination.
 * @param {number} limit - Number of items per page for pagination.
 * @returns {Promise<void>} - A promise that resolves with the bank details.
 * @throws {ApiError} - Throws an error if there is an issue retrieving the bank details.
 */
const getALLBankDetails = catchAsync(async (req, res) => {
	const { page, limit } = req.query;

	const { bankDetails, pagination } = await bankService.getAllBankDetails(
		page,
		limit
	);

	return new ApiSuccess(
		res,
		httpStatus.OK,
		RESPONSE.FETCH_SUCCESS('bank'),
		bankDetails,
		{ pagination }
	);
});

/**
 * Delete a bank account by its ID.
 * @param {number} bankId - The ID of the bank to be deleted.
 * @returns {Promise<void>} - A promise that resolves with the success message.
 * @throws {ApiError} - Throws an error if the bank account is not found.
 */
const deleteBankById = catchAsync(async (req, res) => {
	const { bankId } = req.params;
	const { userId } = req.user;

	await bankService.deleteBankById(bankId, userId);

	return new ApiSuccess(res, httpStatus.OK, RESPONSE.DELETE_SUCCESS('bank'));
});

/**
 * Update a bank account by ID.
 * @param {number} bankId - Bank ID to update.
 * @param {Object} bankDetails - Bank details to update:
 *   - {number} dealer_id
 *   - {string} account_name
 *   - {string} bank_name
 *   - {number} account_number
 *   - {string} ifsc
 *   - {string} branch_name
 * @returns {Promise<void>}
 */
const updateBankById = catchAsync(async (req, res) => {
	const { userId } = req.user;
	const { bankId } = req.params;

	await bankService.updateBankById(bankId, req.body, userId);

	return new ApiSuccess(res, httpStatus.OK, RESPONSE.UPDATE_SUCCESS('bank'));
});

/**
 * Get bank details by ID.
 * @param {number} bankId - The ID of the bank to fetch.
 * @returns {Promise<void>} - A promise that resolves with the bank details.
 * @throws {ApiError} - Throws an error if the bank account is not found.
 */
const getBankById = catchAsync(async (req, res) => {
	const { bankId } = req.params;

	const bank = await bankService.getBankById(bankId);

	return new ApiSuccess(
		res,
		httpStatus.OK,
		RESPONSE.FETCH_SUCCESS('bank'),
		bank
	);
});

module.exports = {
	createDealerBank,
	getALLBankDetails,
	deleteBankById,
	updateBankById,
	getBankById,
};
