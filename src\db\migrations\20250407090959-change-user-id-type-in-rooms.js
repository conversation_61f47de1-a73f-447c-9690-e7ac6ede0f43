/** @type {import('sequelize-cli').Migration} */
module.exports = {
	up: async (queryInterface, Sequelize) => {
		// Step 1: Add a temporary column
		await queryInterface.addColumn('tbl_rooms', 'user_id_temp', {
			type: Sequelize.INTEGER,
			allowNull: true,
		});

		// Step 2: Copy converted data (string to int)
		await queryInterface.sequelize.query(`
      UPDATE tbl_rooms SET user_id_temp = user_id::integer
    `);

		// Step 3: Drop original column
		await queryInterface.removeColumn('tbl_rooms', 'user_id');

		// Step 4: Rename temp column to original name
		await queryInterface.renameColumn('tbl_rooms', 'user_id_temp', 'user_id');
	},

	down: async (queryInterface, Sequelize) => {
		await queryInterface.changeColumn('tbl_rooms', 'user_id', {
			type: Sequelize.STRING,
			allowNull: false,
		});
	},
};
