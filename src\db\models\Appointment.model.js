module.exports = (sequelize, DataTypes) => {
	const TblAppointment = sequelize.define(
		'tbl_appointments',
		{
			id: {
				type: DataTypes.INTEGER,
				autoIncrement: true,
				primaryKey: true,
				allowNull: false,
			},
			dealer_id: {
				type: DataTypes.INTEGER,
				allowNull: true,
				references: {
					model: 'tbl_dealers',
					key: 'id',
				},
				onDelete: 'CASCADE',
			},
			lead_id: {
				type: DataTypes.INTEGER,
				allowNull: true,
				references: {
					model: 'tbl_users',
					key: 'id',
				},
				onDelete: 'CASCADE',
			},
			title: {
				type: DataTypes.TEXT,
				allowNull: false,
			},
			description: {
				type: DataTypes.TEXT,
				allowNull: true,
			},
			start_date: {
				type: DataTypes.DATE,
				allowNull: false,
			},
			start_time: {
				type: DataTypes.TIME,
				allowNull: false,
			},
			meeting_place: {
				type: DataTypes.TEXT,
				allowNull: true,
			},
			created_by: {
				type: DataTypes.INTEGER,
				allowNull: false,
				references: {
					model: 'tbl_wo_roles',
					key: 'id',
				},
				onDelete: 'CASCADE',
				onUpdate: 'CASCADE',
			},
			color: {
				type: DataTypes.STRING,
				allowNull: true,
				defaultValue: '#aab7b7',
			},
			files: {
				type: DataTypes.JSONB, // Use JSONB for PostgreSQL, JSON for other DBs
				allowNull: true,
				defaultValue: [],
				validate: {
					isValidFileArray(value) {
						if (value && !Array.isArray(value)) {
							throw new Error('File must be an array of objects');
						}
						if (
							value &&
							value.some(
								(file) =>
									!file.file_name ||
									!file.file_url ||
									!file.file_type ||
									!file.file_key ||
									!file.file_size ||
									!file.id
							)
						) {
							throw new Error(
								'Each file must have file_name, file_url, file_type, file_key,file_size, and id'
							);
						}
					},
				},
			},
			type: {
				type: DataTypes.ENUM('offline', 'online'),
				allowNull: false,
			},
			specific_member_employee: {
				type: DataTypes.ARRAY(DataTypes.INTEGER),
				allowNull: true,
				default: [],
			},
			reminder: {
				type: DataTypes.ARRAY(DataTypes.STRING),
				allowNull: true,
				defaultValue: null,
				validate: {
					isValidReminder(value) {
						if (value && !Array.isArray(value)) {
							throw new Error('Reminder must be an array');
						}
						if (
							value &&
							value.some(
								(item) => !['30_min', '1_hour', '1_day'].includes(item)
							)
						) {
							throw new Error('Invalid reminder value');
						}
					},
				},
			},
			modified_by: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			deleted_by: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			deleted_at: {
				type: DataTypes.DATE,
				allowNull: true,
			},
			is_deleted: {
				type: DataTypes.BOOLEAN,
				defaultValue: false,
				allowNull: false,
			},
		},
		{
			tableName: 'tbl_appointments',
			timestamps: true,
			createdAt: 'created_at',
			updatedAt: 'updated_at',
			paranoid: true,
			deletedAt: 'deleted_at',
		}
	);

	TblAppointment.associate = (models) => {
		TblAppointment.belongsTo(models.tbl_dealers, {
			foreignKey: 'dealer_id',
			as: 'dealer',
			onDelete: 'CASCADE',
			onUpdate: 'CASCADE',
		});

		TblAppointment.belongsTo(models.tbl_wo_roles, {
			foreignKey: 'created_by',
			as: 'appointment_created_by',
			onDelete: 'CASCADE',
			onUpdate: 'CASCADE',
		});
	};

	TblAppointment.addHook('beforeDestroy', async (appointment, options) => {
		const { deleted_by } = options; // Pass deleted_by through options
		await TblAppointment.update(
			{ is_deleted: true, deleted_by },
			{ where: { id: appointment.id } }
		);
	});

	// Hook to reset `is_deleted` and `deleted_by` on restore
	TblAppointment.addHook('beforeRestore', async (appointment, options) => {
		const updatedAppointment = {
			...appointment.get(), // extract raw values from Sequelize instance
			is_deleted: false,
			deleted_by: null,
		};

		if (options.modified_by) {
			updatedAppointment.modified_by = options.modified_by;
		}

		await TblAppointment.update(updatedAppointment, {
			where: { id: appointment.id },
		});
	});

	// Hook to update `modified_by` when updating the record
	TblAppointment.addHook('beforeUpdate', async (appointment, options) => {
		const { modified_by } = options; // Pass modified_by through options
		await TblAppointment.update(
			{ modified_by },
			{ where: { id: appointment.id } }
		);
	});

	return TblAppointment;
};
