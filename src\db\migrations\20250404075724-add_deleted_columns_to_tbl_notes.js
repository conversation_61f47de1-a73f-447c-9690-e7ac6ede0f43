/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up(queryInterface, Sequelize) {
		const tableDescription = await queryInterface.describeTable('tbl_notes');

		if (!tableDescription.modified_by) {
			await queryInterface.addColumn('tbl_notes', 'modified_by', {
				type: Sequelize.INTEGER,
				allowNull: true,
			});
		}

		if (!tableDescription.deleted_by) {
			await queryInterface.addColumn('tbl_notes', 'deleted_by', {
				type: Sequelize.INTEGER,
				allowNull: true,
			});
		}

		if (!tableDescription.deleted_at) {
			await queryInterface.addColumn('tbl_notes', 'deleted_at', {
				type: Sequelize.DATE,
				allowNull: true,
			});
		}

		if (!tableDescription.is_deleted) {
			await queryInterface.addColumn('tbl_notes', 'is_deleted', {
				type: Sequelize.BOOLEAN,
				defaultValue: false,
				allowNull: false,
			});
		}
	},

	async down(queryInterface) {
		const tableDescription = await queryInterface.describeTable('tbl_notes');

		if (tableDescription.modified_by) {
			await queryInterface.removeColumn('tbl_notes', 'modified_by');
		}

		if (tableDescription.deleted_by) {
			await queryInterface.removeColumn('tbl_notes', 'deleted_by');
		}

		if (tableDescription.deleted_at) {
			await queryInterface.removeColumn('tbl_notes', 'deleted_at');
		}

		if (tableDescription.is_deleted) {
			await queryInterface.removeColumn('tbl_notes', 'is_deleted');
		}
	},
};
