/** @type {import('sequelize-cli').Migration} */
module.exports = {
	up: async (queryInterface, Sequelize) => {
		const tableDescription = await queryInterface.describeTable('tbl_users');
		if (!tableDescription.verification_exp) {
			await queryInterface.addColumn('tbl_users', 'verification_exp', {
				type: Sequelize.DATE,
				allowNull: true,
			});
		}
	},

	down: async (queryInterface) => {
		const tableDescription = await queryInterface.describeTable('tbl_users');
		if (!tableDescription.verification_exp) {
			await queryInterface.removeColumn('tbl_users', 'verification_exp');
		}
	},
};
