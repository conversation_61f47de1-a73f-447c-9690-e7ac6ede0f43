module.exports = (sequelize, DataTypes) => {
	const leadStatusHistory = sequelize.define(
		'tbl_lead_status_history',
		{
			id: {
				type: DataTypes.INTEGER,
				primaryKey: true,
				autoIncrement: true,
				allowNull: false,
			},
			lead_id: {
				type: DataTypes.INTEGER,
				allowNull: false,
			},
			lead_status_id: {
				type: DataTypes.INTEGER,
				allowNull: false,
			},
			created_by: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			created_at: {
				type: DataTypes.DATE,
				allowNull: true,
			},
		},
		{
			tableName: 'tbl_lead_status_history',
			timestamps: false, // Using custom created_date and updated_date
			hooks: {
				// eslint-disable-next-line no-shadow
				beforeCreate: async (leadStatusHistory, options) => {
					// eslint-disable-next-line no-param-reassign
					leadStatusHistory.created_at = new Date();
					if (options.userId) {
						// eslint-disable-next-line no-param-reassign
						leadStatusHistory.created_by = options.userId;
					}
				},
			},
		}
	);

	leadStatusHistory.associate = function (models) {
		leadStatusHistory.belongsTo(models.tbl_users, {
			foreignKey: 'lead_id',
			as: 'leadId',
			onUpdate: 'CASCADE',
			onDelete: 'SET NULL',
		});

		leadStatusHistory.belongsTo(models.leadStatus, {
			foreignKey: 'lead_status_id',
			as: 'status_detail',
			onDelete: 'SET NULL',
		});

		leadStatusHistory.belongsTo(models.tbl_users, {
			foreignKey: 'created_by',
			as: 'creator_detail',
			onDelete: 'SET NULL',
		});
	};

	return leadStatusHistory;
};
