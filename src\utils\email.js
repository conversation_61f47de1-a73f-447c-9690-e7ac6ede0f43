const nodemailer = require('nodemailer');
const config = require('../config/config');

const transport = nodemailer.createTransport(config.email.smtp);

const sendEmail = async (emailData) => {
	const info = await transport.sendMail({
		from: config.email.from,
		to: emailData.to,
		subject: emailData.subject,
		text: emailData.text,
		html: emailData.html,
	});
	return info;
};

module.exports = {
	sendEmail,
};
