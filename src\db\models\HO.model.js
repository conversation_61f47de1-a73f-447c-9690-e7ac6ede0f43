// c:\Users\<USER>\Desktop\your_project\models\tbl_home_owners.js
module.exports = (sequelize, DataTypes) => {
	const TblHomeOwners = sequelize.define(
		'tbl_home_owners',
		{
			id: {
				type: DataTypes.INTEGER,
				autoIncrement: true,
				primaryKey: true,
				allowNull: false,
			},
			first_name: {
				type: DataTypes.STRING(255),
				allowNull: false,
			},
			last_name: {
				type: DataTypes.STRING(255),
				allowNull: true,
			},
			mobile_no: {
				type: DataTypes.STRING(20),
				allowNull: true,
			},
			email_id: {
				type: DataTypes.STRING(255),
				allowNull: false,
			},
			reason: {
				type: DataTypes.TEXT,
				allowNull: true,
			},
			status: {
				type: DataTypes.INTEGER,
				defaultValue: 0,
				allowNull: false,
			},
			created_by: {
				type: DataTypes.INTEGER,
				allowNull: false,
			},
			created_date: {
				type: DataTypes.DATE,
				defaultValue: DataTypes.NOW,
				allowNull: false,
			},
			modified_by: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			modified_date: {
				type: DataTypes.DATE,
				allowNull: true,
			},
		},
		{
			tableName: 'tbl_home_owners',
			timestamps: false, // Set to true if you want Sequelize to manage createdAt and updatedAt fields
		}
	);

	TblHomeOwners.associate = function () {
		// associations can be defined here
	};

	return TblHomeOwners;
};
