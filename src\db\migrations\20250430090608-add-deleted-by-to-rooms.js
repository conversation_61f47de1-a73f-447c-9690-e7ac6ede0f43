/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up(queryInterface, Sequelize) {
		const tableDescription = await queryInterface.describeTable('tbl_rooms');

		if (!tableDescription.deleted_by) {
			await queryInterface.addColumn('tbl_rooms', 'deleted_by', {
				type: Sequelize.INTEGER, // or STRING if it's a name or UUID
				allowNull: true,
			});
		}
	},

	async down(queryInterface) {
		const tableDescription = await queryInterface.describeTable('tbl_rooms');
		if (!tableDescription.deleted_by) {
			await queryInterface.removeColumn('tbl_rooms', 'deleted_by');
		}
	},
};
