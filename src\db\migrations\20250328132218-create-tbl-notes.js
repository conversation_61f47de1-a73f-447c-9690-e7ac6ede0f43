/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up(queryInterface, Sequelize) {
		await queryInterface.createTable('tbl_notes', {
			id: {
				allowNull: false,
				autoIncrement: true,
				primaryKey: true,
				type: Sequelize.INTEGER,
			},
			title: {
				type: Sequelize.STRING,
				allowNull: false,
			},
			description: {
				type: Sequelize.TEXT,
				allowNull: true,
			},
			labels: {
				type: Sequelize.ARRAY(Sequelize.STRING),
				allowNull: true,
				defaultValue: [],
			},
			dealer_id: {
				type: Sequelize.INTEGER,
				allowNull: true,
				references: {
					model: 'tbl_dealers',
					key: 'id',
				},
				onDelete: 'CASCADE',
				onUpdate: 'CASCADE',
			},
			files: {
				type: Sequelize.JSONB, // Use JSONB for PostgreSQL, JSON for other DBs
				allowNull: true,
				defaultValue: [],
				validate: {
					isValidFileArray(value) {
						if (value && !Array.isArray(value)) {
							throw new Error('File must be an array of objects');
						}
						if (
							value &&
							value.some(
								(file) =>
									!file.file_name ||
									!file.file_url ||
									!file.file_type ||
									!file.file_key ||
									!file.file_size ||
									!file.id
							)
						) {
							throw new Error(
								'Each file must have file_name, file_url, file_type, file_key,file_size, and id'
							);
						}
					},
				},
			},
			created_by: {
				type: Sequelize.INTEGER,
				allowNull: false,
			},
			created_at: {
				allowNull: false,
				type: Sequelize.DATE,
			},
			updated_at: {
				allowNull: false,
				type: Sequelize.DATE,
			},
		});
	},
	async down(queryInterface) {
		await queryInterface.dropTable('tbl_notes');
	},
};
