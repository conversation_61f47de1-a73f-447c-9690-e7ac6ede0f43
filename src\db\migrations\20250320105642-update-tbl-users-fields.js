/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up(queryInterface, Sequelize) {
		const tableDesc = await queryInterface.describeTable('tbl_users');

		if (tableDesc.created_date) {
			await queryInterface.removeColumn('tbl_users', 'created_date');
		}

		if (tableDesc.modified_date) {
			await queryInterface.removeColumn('tbl_users', 'modified_date');
		}

		if (!tableDesc.is_rejected) {
			await queryInterface.addColumn('tbl_users', 'is_rejected', {
				type: Sequelize.BOOLEAN,
				allowNull: false,
				defaultValue: false,
			});
		}

		if (!tableDesc.is_blocked) {
			await queryInterface.addColumn('tbl_users', 'is_blocked', {
				type: Sequelize.BOOLEAN,
				allowNull: false,
				defaultValue: false,
			});
		}
	},

	async down(queryInterface, Sequelize) {
		const tableDesc = await queryInterface.describeTable('tbl_users');

		if (!tableDesc.created_date) {
			await queryInterface.addColumn('tbl_users', 'created_date', {
				type: Sequelize.DATE,
				allowNull: true,
			});
		}

		if (!tableDesc.modified_date) {
			await queryInterface.addColumn('tbl_users', 'modified_date', {
				type: Sequelize.DATE,
				allowNull: true,
			});
		}

		// Remove is_rejected and is_blocked if they exist
		if (tableDesc.is_rejected) {
			await queryInterface.removeColumn('tbl_users', 'is_rejected');
		}

		if (tableDesc.is_blocked) {
			await queryInterface.removeColumn('tbl_users', 'is_blocked');
		}
	},
};
