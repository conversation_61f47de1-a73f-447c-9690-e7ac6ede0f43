/** @type {import('sequelize-cli').Migration} */
module.exports = {
	up: async (queryInterface) => {
		const tableDescription = await queryInterface.describeTable('tbl_users');

		if (tableDescription.owner_id) {
			// Column exists – remove it
			await queryInterface.removeColumn('tbl_users', 'owner_id');
		}
	},

	down: async (queryInterface, Sequelize) => {
		const tableDescription = await queryInterface.describeTable('tbl_users');

		if (!tableDescription.owner_id) {
			// Column does not exist – add it back
			await queryInterface.addColumn('tbl_users', 'owner_id', {
				type: Sequelize.INTEGER,
				allowNull: true,
			});
		}
	},
};
