const express = require('express');
const notesLabelController = require('../../controllers/notes_label.controller');
const validateNotesLabel = require('../../validations/notes_label.validation');
const validate = require('../../middlewares/validate');
const { grantAccess } = require('../../middlewares/validateAccessControl');
const { resources } = require('../../config/roles');

const router = express.Router();

// Create a new Notes label
router.post(
	'/add-notes-label',
	grantAccess('create', 'any', resources.ADMIN_CONTROLS),
	validate(validateNotesLabel.notesLabels),
	notesLabelController.addNotesLabel
);

// Update an existing Notes label
router.patch(
	'/update-notes-label/:id',
	grantAccess('create', 'any', resources.ADMIN_CONTROLS),
	validate(validateNotesLabel.notesLabels),
	notesLabelController.updateNotesLabel
);

// Update sorting order for multiple Notes label
router.patch(
	'/update-sort',
	grantAccess('create', 'any', resources.ADMIN_CONTROLS),
	validate(validateNotesLabel.updateSortSchema),
	notesLabelController.updateSortNotesLabel
);

// Retrieve all Notes label
router.get(
	'/get-notes-label',
	grantAccess('create', 'any', resources.ADMIN_CONTROLS),
	notesLabelController.getAllNotesLabel
);

// Soft delete a Notes label
router.delete(
	'/delete-notes-label/:id',
	grantAccess('create', 'any', resources.ADMIN_CONTROLS),
	notesLabelController.deleteNotesLabel
);

module.exports = router;

/**
 * @swagger
 * tags:
 *   name: Notes label
 *   description: API for managing Notes label in the system
 */

/**
 * @swagger
 * /notes-label/add-notes-label:
 *   post:
 *     summary: Create a new Notes label
 *     description: Only authorized users can create a Notes label.
 *     tags: [Notes label]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - title
 *               - is_status
 *             properties:
 *               title:
 *                 type: string
 *                 example: "Status"
 *               is_status:
 *                 type: boolean
 *                 example: true
 *     responses:
 *       "201":
 *         description: Notes label created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Notes label created successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 1
 *                     title:
 *                       type: string
 *                       example: "Status"
 *                     is_status:
 *                       type: boolean
 *                       example: true
 *       "400":
 *         description: Validation error (missing or duplicate title)
 *       "401":
 *         description: Unauthorized (token missing or invalid)
 *       "500":
 *         description: Internal server error
 */

/**
 * @swagger
 * /notes-label/update-notes-label/{id}:
 *   patch:
 *     summary: Update an existing Notes label
 *     description: Only authorized users can update a Notes label.
 *     tags: [Notes label]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: The ID of the Notes label to update.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *                 example: "Updated Notes label"
 *               is_status:
 *                 type: boolean
 *                 example: false
 *     responses:
 *       "200":
 *         description: Notes label updated successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Notes label updated successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 1
 *                     title:
 *                       type: string
 *                       example: "Updated Notes label"
 *                     is_status:
 *                       type: boolean
 *                       example: false
 *       "400":
 *         description: Validation error (invalid ID or duplicate title)
 *       "401":
 *         description: Unauthorized (token missing or invalid)
 *       "404":
 *         description: Notes label not found
 *       "500":
 *         description: Internal server error
 */

/**
 * @swagger
 * /notes-label/update-sort:
 *   patch:
 *     summary: Update sorting order for multiple Notes label
 *     description: Allows admins to update the sort order of multiple Notes label.
 *     tags: [Notes label]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: array
 *             items:
 *               type: object
 *               required:
 *                 - id
 *                 - sort
 *               properties:
 *                 id:
 *                   type: integer
 *                   example: 1
 *                 sort:
 *                   type: integer
 *                   example: 2
 *     responses:
 *       "200":
 *         description: Sorting order updated successfully.
 *       "400":
 *         description: Validation error (invalid or missing fields)
 *       "401":
 *         description: Unauthorized (token missing or invalid)
 *       "500":
 *         description: Internal server error
 */

/**
 * @swagger
 * /notes-label/get-notes-label:
 *   get:
 *     summary: Retrieve all Notes label
 *     description: Fetches a paginated, filterable, and searchable list of active Notes label.
 *     tags: [Notes label]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of Notes label to retrieve per page.
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number to retrieve.
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term to filter Notes label by title.
 *       - in: query
 *         name: order_by
 *         schema:
 *           type: string
 *           enum: [id, title, sort, is_status, created_date]
 *           default: sort
 *         description: Field to order the results by.
 *       - in: query
 *         name: sort
 *         schema:
 *           type: string
 *           enum: [ASC, DESC]
 *           default: ASC
 *         description: Sorting direction (ascending or descending).
 *       - in: query
 *         name: is_status
 *         schema:
 *           type: boolean
 *         description: Filter by active/inactive Notes label.
 *     responses:
 *       200:
 *         description: Successfully retrieved Notes label.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Notes label fetched successfully.
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                         example: 1
 *                       title:
 *                         type: string
 *                         example: Status
 *                       sort:
 *                         type: integer
 *                         example: 1
 *                       is_status:
 *                         type: boolean
 *                         example: true
 *                       created_date:
 *                         type: string
 *                         format: date-time
 *                         example: 2025-04-10T10:00:00.000Z
 *                       updated_date:
 *                         type: string
 *                         format: date-time
 *                         example: 2025-04-10T11:00:00.000Z
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     totalRows:
 *                       type: integer
 *                       example: 50
 *                     filteredRows:
 *                       type: integer
 *                       example: 10
 *                     currentPage:
 *                       type: integer
 *                       example: 1
 *                     totalPages:
 *                       type: integer
 *                       example: 5
 *                     nextPage:
 *                       type: integer
 *                       example: 2
 *                     prevPage:
 *                       type: integer
 *                       example: null
 *                     hasNextPage:
 *                       type: boolean
 *                       example: true
 *                     hasPrevPage:
 *                       type: boolean
 *                       example: false
 *       401:
 *         description: Unauthorized (token missing or invalid)
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /notes-label/delete-notes-label/{id}:
 *   delete:
 *     summary: Soft delete a Notes label
 *     description: Marks a Notes label as deleted instead of permanently removing it.
 *     tags: [Notes label]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Notes label ID to delete.
 *     responses:
 *       "200":
 *         description: Notes label deleted successfully.
 *       "400":
 *         description: Bad request (invalid ID)
 *       "401":
 *         description: Unauthorized (token missing or invalid)
 *       "404":
 *         description: Notes label not found
 *       "500":
 *         description: Internal server error
 */
