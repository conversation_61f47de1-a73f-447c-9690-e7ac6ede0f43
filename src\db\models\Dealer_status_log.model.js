// c:\Users\<USER>\Desktop\your_project\models\tbl_dealer_status_log.js

module.exports = (sequelize, DataTypes) => {
	const TblDealerStatusLog = sequelize.define(
		'tbl_dealer_status_log',
		{
			id: {
				type: DataTypes.INTEGER,
				autoIncrement: true,
				primaryKey: true,
				allowNull: false,
			},
			dealer_id: {
				type: DataTypes.INTEGER,
				allowNull: false,
			},
			status: {
				type: DataTypes.INTEGER,
				allowNull: false,
			},
			approved_by: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			approved_date: {
				type: DataTypes.DATE,
				allowNull: true,
			},
			reason: {
				type: DataTypes.TEXT,
				allowNull: true,
			},
			rejected_by: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			rejected_date: {
				type: DataTypes.DATE,
				allowNull: true,
			},
			created_by: {
				type: DataTypes.INTEGER,
				allowNull: false,
			},
			created_date: {
				type: DataTypes.DATE,
				defaultValue: DataTypes.NOW,
				allowNull: false,
			},
		},
		{
			tableName: 'tbl_dealer_status_log',
			timestamps: false, // Set to true if you want Sequelize to manage createdAt and updatedAt fields
		}
	);

	TblDealerStatusLog.associate = function (models) {
		TblDealerStatusLog.belongsTo(models.tbl_dealers, {
			foreignKey: 'dealer_id',
			targetKey: 'id',
			as: 'dealer', // Optional alias
		});
	};

	return TblDealerStatusLog;
};
