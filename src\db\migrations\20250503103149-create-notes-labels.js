/** @type {import('sequelize-cli').Migration} */
module.exports = {
	up: async (queryInterface, Sequelize) => {
		await queryInterface.createTable('tbl_notes_label', {
			id: {
				type: Sequelize.INTEGER,
				autoIncrement: true,
				primaryKey: true,
				allowNull: false,
			},
			title: {
				type: Sequelize.STRING(100),
				allowNull: false,
				unique: true,
			},
			sort: {
				type: Sequelize.INTEGER,
				allowNull: false,
				defaultValue: 0,
			},
			is_status: {
				type: Sequelize.BOOLEAN,
				allowNull: false,
				defaultValue: false, // 0 means not deleted
				comment: 'true = deleted, false = not deleted', // Add comment
			},
			context: {
				type: Sequelize.STRING(100),
				allowNull: false,
			},
			created_by: {
				type: Sequelize.INTEGER,
				allowNull: true,
			},
			created_date: {
				type: Sequelize.DATE,
				allowNull: false,
				defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
			},
			updated_by: {
				type: Sequelize.INTEGER,
				allowNull: true,
			},
			updated_date: {
				type: Sequelize.DATE,
				allowNull: true,
			},
			deleted: {
				type: Sequelize.BOOLEAN,
				allowNull: false,
				defaultValue: false, // 0 means not deleted
				comment: 'true = deleted, false = not deleted', // Add comment
			},
			deleted_by: {
				type: Sequelize.INTEGER,
				allowNull: true,
			},
			deleted_date: {
				type: Sequelize.DATE,
				allowNull: true,
			},
		});
	},

	down: async (queryInterface) => {
		await queryInterface.dropTable('tbl_notes_label');
	},
};
