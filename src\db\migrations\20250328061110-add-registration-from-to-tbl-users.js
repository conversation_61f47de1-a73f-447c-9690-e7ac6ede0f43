/** @type {import('sequelize-cli').Migration} */
module.exports = {
	up: async (queryInterface, Sequelize) => {
		await queryInterface.addColumn('tbl_users', 'registration_from', {
			type: Sequelize.STRING(50), // Adjust the type and length as needed
			allowNull: true, // Set to false if you want to make it required
		});
	},

	down: async (queryInterface) => {
		await queryInterface.removeColumn('tbl_users', 'registration_from');
	},
};
