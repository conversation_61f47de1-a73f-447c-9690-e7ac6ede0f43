/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up(queryInterface, Sequelize) {
		const table = await queryInterface.describeTable('tbl_notes');

		if (!table.lead_id) {
			await queryInterface.addColumn('tbl_notes', 'lead_id', {
				type: Sequelize.INTEGER,
				allowNull: true,
				references: {
					model: 'tbl_users',
					key: 'id',
				},
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			});
		}
	},

	async down(queryInterface) {
		const table = await queryInterface.describeTable('tbl_notes');

		if (table.lead_id) {
			await queryInterface.removeColumn('tbl_notes', 'lead_id');
		}
	},
};
