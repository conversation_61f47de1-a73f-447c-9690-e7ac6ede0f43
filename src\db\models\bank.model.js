module.exports = (sequelize, DataTypes) => {
	const TblBank = sequelize.define(
		'tbl_banks',
		{
			id: {
				allowNull: false,
				autoIncrement: true,
				primaryKey: true,
				type: DataTypes.INTEGER,
			},
			dealer_id: {
				type: DataTypes.INTEGER,
				allowNull: true,
				references: {
					model: 'tbl_dealers',
					key: 'id',
				},
				onDelete: 'CASCADE',
			},
			account_name: {
				type: DataTypes.BLOB,
				allowNull: false,
			},
			bank_name: {
				type: DataTypes.BLOB,
				allowNull: false,
			},
			account_number: {
				type: DataTypes.BLOB,
				allowNull: false,
			},
			ifsc: {
				type: DataTypes.BLOB,
				allowNull: false,
			},
			branch_name: {
				type: DataTypes.BLOB,
				allowNull: false,
			},
			created_at: {
				allowNull: false,
				type: DataTypes.DATE,
			},
			updated_at: {
				allowNull: false,
				type: DataTypes.DATE,
			},
			is_deleted: {
				type: DataTypes.BOOLEAN,
				defaultValue: false,
				allowNull: false,
			},
			deleted_at: {
				type: DataTypes.DATE,
				allowNull: true,
			},
			created_by: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			modified_by: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			deleted_by: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
		},
		{
			tableName: 'tbl_banks',
			paranoid: true,
			timestamps: true,
			createdAt: 'created_at',
			updatedAt: 'updated_at',
			deletedAt: 'deleted_at',
		}
	);

	TblBank.addHook('beforeDestroy', async (bank, options) => {
		const { deleted_by } = options; // Pass deleted_by through options
		await TblBank.update(
			{ is_deleted: true, deleted_by },
			{ where: { id: bank.id } }
		);
	});

	// Hook to reset `is_deleted` and `deleted_by` on restore
	TblBank.addHook('beforeRestore', async (bank, options) => {
		// eslint-disable-next-line no-unused-expressions, no-param-reassign, no-sequences
		(bank.is_deleted = false), (bank.deleted_by = null);
		if (options.modified_by) {
			// eslint-disable-next-line no-param-reassign
			bank.modified_by = options.modified_by;
		}
		await bank.save();
	});

	// Hook to update `modified_by` when updating the record
	TblBank.addHook('beforeUpdate', async (bank, options) => {
		const { modified_by } = options; // Pass modified_by through options
		await TblBank.update({ modified_by }, { where: { id: bank.id } });
	});

	TblBank.associate = function (models) {
		TblBank.belongsTo(models.tbl_dealers, {
			foreignKey: 'dealer_id',
			as: 'dealer',
		});
	};

	return TblBank;
};
