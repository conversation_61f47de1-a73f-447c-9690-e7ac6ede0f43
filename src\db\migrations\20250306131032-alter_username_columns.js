/** @type {import('sequelize-cli').Migration} */
module.exports = {
	up: async (queryInterface, Sequelize) => {
		// Alter the column type
		await queryInterface.changeColumn('tbl_temp_user', 'first_name', {
			type: Sequelize.STRING(400),
			allowNull: false,
		});
		await queryInterface.changeColumn('tbl_temp_user', 'last_name', {
			type: Sequelize.STRING(400),
			allowNull: true,
		});
		await queryInterface.changeColumn('tbl_temp_user', 'primary_email', {
			type: Sequelize.STRING(400),
			allowNull: true,
		});
		await queryInterface.changeColumn('tbl_temp_user', 'unique_id', {
			type: Sequelize.STRING(400),
			allowNull: false,
		});
		await queryInterface.changeColumn('tbl_wo_roles', 'role_name', {
			type: Sequelize.STRING(400),
			allowNull: false,
		});
		await queryInterface.changeColumn('tbl_wo_roles', 'slug', {
			type: Sequelize.STRING(400),
			allowNull: false,
		});
		await queryInterface.changeColumn('tbl_dealer_roles', 'role_name', {
			type: Sequelize.STRING(400),
			allowNull: false,
		});
		await queryInterface.changeColumn('tbl_dealer_roles', 'slug', {
			type: Sequelize.STRING(400),
			allowNull: false,
		});
		await queryInterface.changeColumn('tbl_dealers', 'first_name', {
			type: Sequelize.STRING(400),
			allowNull: false,
		});
		await queryInterface.changeColumn('tbl_dealers', 'last_name', {
			type: Sequelize.STRING(400),
			allowNull: true,
		});
		await queryInterface.changeColumn('tbl_dealers', 'mobile_no', {
			type: Sequelize.STRING(400),
			allowNull: true,
		});
		await queryInterface.changeColumn('tbl_home_owners', 'first_name', {
			type: Sequelize.STRING(400),
			allowNull: false,
		});
		await queryInterface.changeColumn('tbl_home_owners', 'last_name', {
			type: Sequelize.STRING(400),
			allowNull: true,
		});
		await queryInterface.changeColumn('tbl_home_owners', 'mobile_no', {
			type: Sequelize.STRING(400),
			allowNull: true,
		});
	},

	down: async (queryInterface, Sequelize) => {
		// Revert back to ARRAY type (if needed)
		await queryInterface.changeColumn('tbl_temp_user', 'first_name', {
			type: Sequelize.ARRAY(Sequelize.STRING(400)),
			allowNull: false,
		});
		await queryInterface.changeColumn('tbl_temp_user', 'last_name', {
			type: Sequelize.ARRAY(Sequelize.STRING(400)),
			allowNull: true,
		});
		await queryInterface.changeColumn('tbl_temp_user', 'primary_email', {
			type: Sequelize.ARRAY(Sequelize.STRING(400)),
			allowNull: true,
		});
		await queryInterface.changeColumn('tbl_temp_user', 'unique_id', {
			type: Sequelize.ARRAY(Sequelize.STRING(400)),
			allowNull: false,
		});
		await queryInterface.changeColumn('tbl_wo_roles', 'role_name', {
			type: Sequelize.ARRAY(Sequelize.STRING(400)),
			allowNull: false,
		});
		await queryInterface.changeColumn('tbl_wo_roles', 'slug', {
			type: Sequelize.ARRAY(Sequelize.STRING(400)),
			allowNull: false,
		});
		await queryInterface.changeColumn('tbl_dealer_roles', 'role_name', {
			type: Sequelize.ARRAY(Sequelize.STRING(400)),
			allowNull: false,
		});
		await queryInterface.changeColumn('tbl_dealer_roles', 'slug', {
			type: Sequelize.ARRAY(Sequelize.STRING(400)),
			allowNull: false,
		});
		await queryInterface.changeColumn('tbl_dealers', 'first_name', {
			type: Sequelize.ARRAY(Sequelize.STRING(400)),
			allowNull: false,
		});
		await queryInterface.changeColumn('tbl_dealers', 'last_name', {
			type: Sequelize.ARRAY(Sequelize.STRING(400)),
			allowNull: true,
		});
		await queryInterface.changeColumn('tbl_dealers', 'mobile_no', {
			type: Sequelize.ARRAY(Sequelize.STRING(400)),
			allowNull: true,
		});
		await queryInterface.changeColumn('tbl_home_owners', 'first_name', {
			type: Sequelize.ARRAY(Sequelize.STRING(400)),
			allowNull: false,
		});
		await queryInterface.changeColumn('tbl_home_owners', 'last_name', {
			type: Sequelize.ARRAY(Sequelize.STRING(400)),
			allowNull: true,
		});
		await queryInterface.changeColumn('tbl_home_owners', 'mobile_no', {
			type: Sequelize.ARRAY(Sequelize.STRING(400)),
			allowNull: true,
		});
	},
};
