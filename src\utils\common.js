const crypto = require('crypto');

const generateOTP = () => {
	return Math.floor(100000 + Math.random() * 900000).toString();
};

const generateUniqueId = (prefix = 'DR') => {
	const timestamp = Date.now().toString();
	const random = crypto.randomBytes(3).toString('hex').toUpperCase();
	return `${prefix}${timestamp.slice(-6)}${random}`;
};

const generateProfileImagePath = (filename) => {
	return `uploads/dealers/profiles/${filename}`;
};

module.exports = {
	generateOTP,
	generateUniqueId,
	generateProfileImagePath,
};
