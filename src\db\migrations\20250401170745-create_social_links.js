/** @type {import('sequelize-cli').Migration} */
module.exports = {
	up: async (queryInterface, Sequelize) => {
		await queryInterface.createTable(
			'tbl_social_links',
			{
				id: {
					type: Sequelize.INTEGER,
					autoIncrement: true,
					primaryKey: true,
					allowNull: false,
				},
				facebook: {
					type: Sequelize.STRING(100),
					allowNull: true,
				},
				twitter: {
					type: Sequelize.STRING(100),
					allowNull: true,
				},
				linkedin: {
					type: Sequelize.STRING(100),
					allowNull: true,
				},
				instagram: {
					type: Sequelize.STRING(100),
					allowNull: true,
				},
				youtube: {
					type: Sequelize.STRING(100),
					allowNull: true,
				},
				pinterest: {
					type: Sequelize.STRING(100),
					allowNull: true,
				},
				whatsapp: {
					type: Sequelize.STRING(100),
					allowNull: true,
				},
				tiktok: {
					type: Sequelize.STRING(100),
					allowNull: true,
				},
				user_id: {
					type: Sequelize.INTEGER,
					allowNull: false,
				},
				created_by: {
					type: Sequelize.INTEGER,
					allowNull: true,
				},
				created_date: {
					type: Sequelize.DATE,
					allowNull: false,
					defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
				},
				updated_by: {
					type: Sequelize.INTEGER,
					allowNull: true,
				},
				updated_date: {
					type: Sequelize.DATE,
					allowNull: true,
				},
				deleted: {
					type: Sequelize.BOOLEAN,
					allowNull: false,
					defaultValue: false, // 0 means not deleted
					comment: 'true = deleted, false = not deleted', // Add comment
				},
				deleted_by: {
					type: Sequelize.INTEGER,
					allowNull: true,
				},
				deleted_date: {
					type: Sequelize.DATE,
					allowNull: true,
				},
			},
			{
				tableName: 'tbl_social_links',
				timestamps: true, // Enables Sequelize's automatic timestamps
				createdAt: 'created_date', // Map Sequelize `createdAt` to `created_date`
				updatedAt: 'updated_date', // Map Sequelize `updatedAt` to `updated_date`
				paranoid: true, // Enables soft delete
				deletedAt: 'deleted_date', // Map Sequelize `deletedAt` to `deleted_date`
			}
		);
	},

	down: async (queryInterface) => {
		await queryInterface.dropTable('tbl_social_links');
	},
};
