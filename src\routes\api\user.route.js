const express = require('express');
const validate = require('../../middlewares/validate');
const userValidation = require('../../validations/user.validation');
const {
	updateProfile,
	getUserProfile,
	changePassword,
	createLoginPin,
} = require('../../controllers/user.controller');
const authMiddleware = require('../../middlewares/authMiddleware');

const router = express.Router();
/**
 * @namespace UserRoutes
 * @description User management routes
 */

/**
 * @name GET /api/v1/users/my-profile
 * @function
 * @memberof UserRoutes
 * @description Retrieves the authenticated user's profile
 * @middleware authMiddleware - Ensures user is authenticated
 * @returns {Object} User profile data
 */
router.get('/my-profile', authMiddleware, getUserProfile);

/**
 * @name PATCH /api/v1/users/update-profile/:id
 * @function
 * @memberof UserRoutes
 * @description Updates user profile information
 * @middleware authMiddleware - Ensures user is authenticated
 * @middleware validate - Validates request payload
 * @param {string} id - User ID
 * @returns {Object} Success message and updated user data
 */
router.patch(
	'/update-profile/:id',
	authMiddleware,
	validate(userValidation.validateUserInfo),
	updateProfile
);

/**
 * @name PATCH /api/v1/users/change-password
 * @function
 * @memberof UserRoutes
 * @description Changes user's password
 * @middleware authMiddleware - Ensures user is authenticated
 * @middleware validate - Validates password requirements
 * @returns {Object} Success message
 */
router.patch(
	'/change-password',
	authMiddleware,
	validate(userValidation.changePassword),
	changePassword
);

module.exports = router;

/**
 * @swagger
 * tags:
 *   name: Users
 *   description: User management and retrieval
 */

/**
 * @swagger
 * /users/my-profile:
 *   get:
 *     summary: Fetch user profile
 *     description: Retrieves the user profile information from the tbl_users table based on the authenticated user's ID.
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User profile fetched successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status_code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "User profile fetched successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 1
 *                     first_name:
 *                       type: string
 *                       example: "John"
 *                     last_name:
 *                       type: string
 *                       example: "Doe"
 *                     mobile_no:
 *                       type: string
 *                       example: "1234567890"
 *                     primary_email:
 *                       type: string
 *                       example: "<EMAIL>"
 *                     website:
 *                       type: string
 *                       example: "https://example.com"
 *                     company_name:
 *                       type: string
 *                       example: "Example Corp"
 *                     company_phone:
 *                       type: string
 *                       example: "9876543210"
 *                     address:
 *                       type: string
 *                       example: "123 Example St"
 *                     city:
 *                       type: string
 *                       example: "Example City"
 *                     state:
 *                       type: string
 *                       example: "Example State"
 *                     country:
 *                       type: string
 *                       example: "Example Country"
 *                     zip:
 *                       type: string
 *                       example: "12345"
 *       404:
 *         description: User not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "User not found"
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "You are not authorized to access this profile."
 */
/**
 * @swagger
 * /users/update-profile/{id}:
 *   patch:
 *     summary: Update user profile
 *     description: Updates the user profile information in the tbl_users table and corresponding homeowner or dealer details.
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: id
 *         in: path
 *         required: true
 *         description: ID of the user to update
 *         schema:
 *           type: integer
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               firstName:
 *                 type: string
 *                 example: "John"
 *               lastName:
 *                 type: string
 *                 example: "Doe"
 *               mobileNo:
 *                 type: string
 *                 example: "1234567890"
 *               emailId:
 *                 type: string
 *                 example: "<EMAIL>"
 *               website:
 *                 type: string
 *                 example: "https://example.com"
 *               companyName:
 *                 type: string
 *                 example: "Example Corp"
 *               companyPhone:
 *                 type: string
 *                 example: "9876543210"
 *               address:
 *                 type: string
 *                 example: "123 Example St"
 *               city:
 *                 type: string
 *                 example: "Example City"
 *               state:
 *                 type: string
 *                 example: "Example State"
 *               country:
 *                 type: string
 *                 example: "Example Country"
 *               zip:
 *                 type: string
 *                 example: "12345"
 *               image_base64:
 *                 type: string
 *                 example: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."
 *     responses:
 *       200:
 *         description: User information updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status_code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "User information updated successfully"
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "Invalid input data. Please check the provided information."
 *       404:
 *         description: User not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   example: "User not found"
 */
/**
 * @swagger
 * /users/change-password:
 *   patch:
 *     summary: Change user password
 *     description: Change logged-in user's password
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []     # Requires bearer token
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - currentPassword
 *               - newPassword
 *               - confirmPassword
 *             properties:
 *               currentPassword:
 *                 type: string
 *                 minLength: 8
 *                 description: Current password
 *               newPassword:
 *                 type: string
 *                 minLength: 8
 *                 description: New password
 *               confirmPassword:
 *                 type: string
 *                 minLength: 8
 *                 description: Confirm new password
 *     responses:
 *       "200":
 *         description: Password changed successfully
 *       "400":
 *         description: Invalid input data
 *       "401":
 *         description: Unauthorized - Invalid token or session
 */

/**
 * @name POST /api/users/create-pin
 * @function
 * @memberof UserRoutes
 * @description Creates or updates user's login PIN
 */
router.post(
	'/create-pin',
	authMiddleware,
	validate(userValidation.createLoginPin),
	createLoginPin
);

/**
 * @swagger
 * /users/create-pin:
 *   post:
 *     summary: Create or update login PIN
 *     description: Creates or updates the user's 6-digit login PIN. PIN must be unique across all users.
 *     tags: [Users]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - pin
 *             properties:
 *               pin:
 *                 type: string
 *                 minLength: 6
 *                 maxLength: 6
 *                 pattern: '^[0-9]{6}$'
 *                 example: "123456"
 *                 description: Must be exactly 6 digits and unique across all users
 *     responses:
 *       "200":
 *         description: PIN created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Login PIN created successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     userId:
 *                       type: number
 *                       example: 1
 *       "400":
 *         description: Invalid PIN format
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "PIN must be exactly 6 digits"
 *       "409":
 *         description: PIN already in use
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "This PIN is already in use. Please choose a different PIN"
 */
