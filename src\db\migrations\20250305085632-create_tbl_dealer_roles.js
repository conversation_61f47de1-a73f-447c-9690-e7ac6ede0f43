/** @type {import('sequelize-cli').Migration} */
// c:\Users\<USER>\Desktop\your_project\migrations\20231010_create_tbl_dealer_roles.js

module.exports = {
	up: (queryInterface, Sequelize) =>
		queryInterface.createTable('tbl_dealer_roles', {
			id: {
				type: Sequelize.INTEGER,
				allowNull: false,
				primaryKey: true,
				autoIncrement: true,
			},
			role_name: {
				type: Sequelize.ARRAY(Sequelize.STRING(255)),
				allowNull: false,
			},
			slug: {
				type: Sequelize.ARRAY(Sequelize.STRING(255)),
				allowNull: false,
			},
			created_date: {
				type: Sequelize.DATE,
				defaultValue: Sequelize.NOW,
				allowNull: false,
			},
			created_by: {
				type: Sequelize.INTEGER,
				allowNull: false,
			},
			updated_date: {
				type: Sequelize.DATE,
				allowNull: true,
			},
			updated_by: {
				type: Sequelize.INTEGER,
				allowNull: true,
			},
			dealer_id: {
				type: Sequelize.INTEGER,
				allowNull: true,
			},
			is_active: {
				type: Sequelize.INTEGER,
				defaultValue: 1,
				allowNull: false,
			},
			is_deleted: {
				type: Sequelize.BOOLEAN,
				defaultValue: false,
			},
			deleted_by: {
				type: Sequelize.INTEGER,
				allowNull: true,
			},
			deleted_date: {
				type: Sequelize.DATE,
				allowNull: true,
			},
		}),
	down: (queryInterface /* , Sequelize */) =>
		queryInterface.dropTable('tbl_dealer_roles'),
};
