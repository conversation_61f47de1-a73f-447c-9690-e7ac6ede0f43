module.exports = {
	up: async (queryInterface) => {
		await queryInterface.bulkInsert(
			'tbl_wo_roles',
			[
				{
					id: 1,
					role_name: 'ADMI<PERSON>',
					slug: 'ADMIN',
					created_date: new Date(),
					created_by: 1,
					is_active: 1,
				},
				{
					id: 2,
					role_name: 'HOMEOWNER',
					slug: 'HOMEOWNER',
					created_date: new Date(),
					created_by: 1,
					is_active: 1,
				},
				{
					id: 3,
					role_name: '<PERSON><PERSON><PERSON>',
					slug: 'DEAL<PERSON>',
					created_date: new Date(),
					created_by: 1,
					is_active: 1,
				},
			],
			{}
		);
	},

	down: async (queryInterface) => {
		await queryInterface.bulkDelete('tbl_wo_roles', null, {});
	},
};
