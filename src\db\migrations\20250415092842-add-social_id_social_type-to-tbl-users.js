/** @type {import('sequelize-cli').Migration} */
module.exports = {
	up: async (queryInterface, Sequelize) => {
		const tableDescription = await queryInterface.describeTable('tbl_users');
		if (!tableDescription.social_id) {
			await queryInterface.addColumn('tbl_users', 'social_id', {
				type: Sequelize.STRING,
				allowNull: true,
			});
		}

		if (!tableDescription.social_type) {
			await queryInterface.addColumn('tbl_users', 'social_type', {
				type: Sequelize.ENUM('google', 'facebook'),
				allowNull: true,
			});
		}
	},

	down: async (queryInterface) => {
		const tableDescription = await queryInterface.describeTable('tbl_users');
		if (!tableDescription.social_id) {
			await queryInterface.removeColumn('tbl_users', 'social_id');
		}
		if (!tableDescription.social_type) {
			await queryInterface.removeColumn('tbl_users', 'social_type');
		}
	},
};
