/** @type {import('sequelize-cli').Migration} */
module.exports = {
	up: async (queryInterface, Sequelize) => {
		const role = {
			id: 4,
			role_name: 'LEAD',
			slug: 'lead',
			created_date: new Date(),
			created_by: 1,
			is_active: 1,
		};

		// Ensure ID is not undefined before proceeding
		if (!role.id) {
			return;
		}

		const existingRole = await queryInterface.sequelize.query(
			`SELECT id FROM tbl_wo_roles WHERE id = :id`,
			{
				replacements: { id: role.id },
				type: Sequelize.QueryTypes.SELECT,
			}
		);

		if (existingRole.length > 0) {
			// Update the existing role
			await queryInterface.bulkUpdate(
				'tbl_wo_roles',
				{
					role_name: role.role_name,
					slug: role.slug,
					created_date: role.created_date,
					created_by: role.created_by,
					is_active: role.is_active,
				},
				{ id: role.id } // Ensure 'id' is not undefined
			);
		} else {
			// Insert the new role
			await queryInterface.bulkInsert('tbl_wo_roles', [role], {});
		}
	},

	down: async (queryInterface) => {
		await queryInterface.bulkDelete('tbl_wo_roles', { id: 4 }, {});
	},
};
