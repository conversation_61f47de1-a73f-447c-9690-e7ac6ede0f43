/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up(queryInterface, Sequelize) {
		const tableDescription = await queryInterface.describeTable('tbl_rooms');

		if (!tableDescription.deleted_at) {
			await queryInterface.addColumn('tbl_rooms', 'deleted_at', {
				type: Sequelize.DATE,
				allowNull: true,
			});
		}
	},

	async down(queryInterface) {
		const tableDescription = await queryInterface.describeTable('tbl_rooms');

		if (!tableDescription.deleted_at) {
			await queryInterface.removeColumn('tbl_rooms', 'deleted_at');
		}
	},
};
