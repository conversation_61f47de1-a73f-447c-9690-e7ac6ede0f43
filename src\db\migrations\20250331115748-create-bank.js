/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up(queryInterface, Sequelize) {
		await queryInterface.createTable('tbl_banks', {
			id: {
				allowNull: false,
				autoIncrement: true,
				primaryKey: true,
				type: Sequelize.INTEGER,
			},
			dealer_id: {
				type: Sequelize.INTEGER,
				allowNull: true,
				unique: true,
				references: {
					model: 'tbl_dealers',
					key: 'id',
				},
				onDelete: 'CASCADE',
			},
			account_name: {
				type: Sequelize.BLOB,
				allowNull: false,
			},
			bank_name: {
				type: Sequelize.BLOB,
				allowNull: false,
			},
			account_number: {
				type: Sequelize.BLOB,
				allowNull: false,
			},
			ifsc: {
				type: Sequelize.BLOB,
				allowNull: false,
			},
			branch_name: {
				type: Sequelize.BLOB,
				allowNull: false,
			},
			is_deleted: {
				type: Sequelize.BOOLEAN,
				defaultValue: false,
				allowNull: false,
			},
			deleted_at: {
				type: Sequelize.DATE,
				allowNull: true,
			},
			created_at: {
				allowNull: false,
				type: Sequelize.DATE,
			},
			updated_at: {
				allowNull: false,
				type: Sequelize.DATE,
			},
		});
	},
	async down(queryInterface) {
		await queryInterface.dropTable('tbl_banks');
	},
};
