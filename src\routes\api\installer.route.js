const express = require('express');
const installerController = require('../../controllers/installer.controller');

const router = express.Router();

router.get('/installer-lists', installerController.getInstallerList);

router.get('/installer-list/:id', installerController.getInstallerListByid);

router.post('/create-installer', installerController.addOrupdateInstaller);

router.delete('/delete-installer/', installerController.deleteInstallerByid);

router.get('/export-installer/', installerController.exportToInstallerTable);

module.exports = router;

/**
 * @swagger
 * tags:
 *   name: Installer
 *   description: installer management
 */

/**
 * @swagger
 * /installer/installer-lists:
 *    get:
 *      summary: Get all installer list
 *      description: Only admins can retrieve all installer.
 *      tags: [Installer]
 *      security:
 *        - bearerAuth: []
 *      parameters:
 *        - in: query
 *          name: limit
 *          schema:
 *            type: integer
 *            minimum: 1
 *          default: 10
 *          description: Maximum number of users
 *        - in: query
 *          name: page
 *          schema:
 *            type: integer
 *            minimum: 1
 *            default: 1
 *          description: Page number
 *      responses:
 *        "200":
 *          description: Successfully retrieved all record.
 *        "401":
 *          description: Unauthorized access.
 *        "500":
 *           description: Internal server error.
 */

/**
 * @swagger
 * /installer/installer-list/{id}:
 *    get:
 *      summary: Get all installer list by id
 *      description: Only admins can retrieve all installer.
 *      tags: [Installer]
 *      security:
 *        - bearerAuth: []
 *      parameters:
 *        - in: path
 *          name: id
 *          required: true
 *          schema:
 *            type: string
 *          description: User id
 *      responses:
 *        "200":
 *          description: Successfully retrieved all record.
 *        "401":
 *          description: Unauthorized access.
 *        "500":
 *           description: Internal server error.
 */

/**
 * @swagger
 * /installer/create-installer:
 *   post:
 *     summary: Add or Update an Installer
 *     description: Adds a new installer if no ID is provided, or updates an existing installer if an ID is provided.
 *     tags:
 *       - Installer
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - email
 *               - password
 *               - role
 *             properties:
 *               id:
 *                 type: integer
 *                 example: 1 (if updating)
 *                 description: Installer ID (required for updates)
 *               name:
 *                 type: string
 *                 example: "John Doe"
 *                 description: "Full name of the installer"
 *               email:
 *                 type: string
 *                 format: email
 *                 example: "<EMAIL>"
 *                 description: "Email of the installer"
 *               phone:
 *                 type: string
 *                 example: "+1 **********"
 *                 description: "phone number for the installer"
 *               dealer_id:
 *                 type: integer
 *                 example: "1"
 *                 description: "ID of the associated dealer"
 *               status:
 *                 type: integer
 *                 example: "1"
 *                 description: "'1' for active, '0' for inactive"
 *               created_by:
 *                 type: integer
 *                 example: "1"
 *                 description:  "User ID of the person who created the installer"
 *               updated_by:
 *                 type: integer
 *                 example: "1"
 *                 description:  "User ID of the last person who updated this entry"
 *     responses:
 *       "200":
 *         description: Installer added or updated successfully
 *       "400":
 *         description: Validation error
 *       "401":
 *         description: Unauthorized access
 *       "404":
 *         description: Installer not found (for updates)
 *       "500":
 *         description: Internal server error
 */

/**
 * @swagger
 * /installer/delete-installer:
 *   delete:
 *     summary: Delete Installer
 *     description: Only admins can delete other users.
 *     tags:
 *       - Installer
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - id
 *               - userId
 *             properties:
 *               id:
 *                 type: integer
 *                 example: 1
 *                 description: Installer ID (required for delete)
 *               userId:
 *                 type: integer
 *                 example: 1
 *                 description: login user id
 *     responses:
 *       "200":
 *         description: Installer delete successfully
 *       "400":
 *         description: Validation error
 *       "401":
 *         description: Unauthorized access
 *       "404":
 *         description: Installer not found
 *       "500":
 *         description: Internal server error
 */
