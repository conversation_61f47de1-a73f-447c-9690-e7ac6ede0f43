const httpStatus = require('http-status');
const bcrypt = require('bcrypt');
const ApiSuccess = require('../utils/ApiSuccess');
const catchAsync = require('../utils/catchAsync');
const { dealerService } = require('../services');
const RESPONSE = require('../utils/ApiResponseMsg');

const getDealer = catchAsync(async (req, res) => {
	const { userId } = req.user;

	const dealer = await dealerService.getUserById(userId);

	const flattendDealer = {
		...dealer.get({ plain: true }),
		...dealer.dealer?.get({ plain: true }),
	};

	delete flattendDealer.dealer;

	return new ApiSuccess(
		res,
		httpStatus.OK,
		RESPONSE.FETCH_SUCCESS('dealer'),
		flattendDealer
	);
});

const updateDealerById = catchAsync(async (req, res) => {
	const { userId } = req.user;

	const { password } = req.body;

	if (password) {
		req.body.password = await bcrypt.hash(password, 10);
	}

	const updatedDealer = await dealerService.updateDealerById(
		userId,
		req.body,
		userId
	);

	return new ApiSuccess(
		res,
		httpStatus.OK,
		RESPONSE.UPDATE_SUCCESS('dealer'),
		updatedDealer
	);
});

module.exports = {
	getDealer,
	updateDealerById,
};
