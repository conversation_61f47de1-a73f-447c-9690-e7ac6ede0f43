const Joi = require('@hapi/joi');

// Custom validation for Base64 image
const base64Image = (value, helpers) => {
	const regex = /^data:image\/(jpeg|jpg|png|gif);base64,[A-Za-z0-9+/=]+$/;
	if (!regex.test(value)) {
		return helpers.error('any.invalid'); // Trigger a validation error
	}
	return value; // Return the value if valid
};

const validateRoom = (req, res, next) => {
	const schema = Joi.object({
		user_id: Joi.number().integer().required(), // Ensure user_id is an integer
		room_name: Joi.string().required(), // Single room_name (no rooms array)
		windows: Joi.array()
			.items(
				Joi.object({
					window_name: Joi.string().required(),
					image_base64: Joi.string().custom(base64Image).required(), // Use custom validation
					width: Joi.number().required(),
					height: Joi.number().required(),
				})
			)
			.min(1)
			.required(), // At least one window required
	});

	const { error } = schema.validate(req.body, { abortEarly: false });
	if (error) {
		// Create a custom error response
		const errorResponse = error.details.map((err) => {
			return {
				field: err.path.join('.'), // Join the path to create a field reference
				message: err.message, // The error message
			};
		});

		return res.status(400).json({
			success: false,
			message: 'Validation failed',
			errors: errorResponse,
		});
	}
	next();
};

// Validation schema for updating a room
const updateRoomDetail = {
	params: Joi.object().keys({
		roomId: Joi.number().integer().positive().required().messages({
			'number.base': 'Room ID must be a number',
			'number.positive': 'Room ID must be positive',
			'any.required': 'Room ID is required',
		}),
	}),
	body: Joi.object().keys({
		room_name: Joi.string().required().min(1).max(100).messages({
			'string.empty': 'Room name cannot be empty',
			'string.min': 'Room name must be at least 1 character long',
			'string.max': 'Room name cannot exceed 100 characters',
		}),
		windows: Joi.array()
			.items(
				Joi.object({
					id: Joi.number().integer().positive().optional().messages({
						'number.base': 'Window ID must be a number',
						'number.positive': 'Window ID must be positive',
					}),
					window_name: Joi.string().required().min(1).max(100).messages({
						'string.empty': 'Window name cannot be empty',
						'string.min': 'Window name must be at least 1 character long',
						'string.max': 'Window name cannot exceed 100 characters',
					}),
					width: Joi.number().positive().required().messages({
						'number.base': 'Width must be a number',
						'number.positive': 'Width must be positive',
						'any.required': 'Width is required',
					}),
					height: Joi.number().positive().required().messages({
						'number.base': 'Height must be a number',
						'number.positive': 'Height must be positive',
						'any.required': 'Height is required',
					}),
					image_base64: Joi.string()
						.custom(base64Image)
						.when('id', {
							is: Joi.exist(),
							then: Joi.optional(),
							otherwise: Joi.required(),
						})
						.messages({
							'any.invalid':
								'Invalid image format. Must be base64 encoded JPEG, PNG, or GIF',
							'any.required': 'Image is required for new windows',
						}),
				})
			)
			.min(0)
			.default([])
			.messages({
				'array.base': 'Windows must be an array',
			}),
		windows_to_delete: Joi.array()
			.items(
				Joi.number().integer().positive().messages({
					'number.base': 'Window ID to delete must be a number',
					'number.positive': 'Window ID to delete must be positive',
				})
			)
			.unique()
			.optional()
			.default([])
			.messages({
				'array.base': 'Windows to delete must be an array',
				'array.unique': 'Duplicate window IDs in deletion list',
			}),
	}),
};

module.exports = {
	validateRoom,
	updateRoomDetail,
};
