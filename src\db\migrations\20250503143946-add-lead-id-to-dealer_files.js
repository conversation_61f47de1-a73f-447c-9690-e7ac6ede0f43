/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up(queryInterface, Sequelize) {
		await queryInterface.sequelize.query(`
      ALTER TABLE tbl_dealer_files
      ALTER COLUMN dealer_id DROP NOT NULL;
    `);

		const table = await queryInterface.describeTable('tbl_dealer_files');

		// 1. Make dealer_id nullable
		if (table.dealer_id && !table.dealer_id.allowNull) {
			await queryInterface.changeColumn('tbl_dealer_files', 'dealer_id', {
				type: Sequelize.INTEGER,
				allowNull: true,
				references: {
					model: 'tbl_users',
					key: 'id',
				},
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			});
		}

		// 2. Add lead_id if it doesn't exist
		if (!table.lead_id) {
			await queryInterface.addColumn('tbl_dealer_files', 'lead_id', {
				type: Sequelize.INTEGER,
				allowNull: true,
				references: {
					model: 'tbl_users',
					key: 'id',
				},
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			});
		}
	},

	async down(queryInterface, Sequelize) {
		const table = await queryInterface.describeTable('tbl_dealer_files');

		// Revert dealer_id to NOT NULL if originally not nullable (optional, depending on your rollback strategy)
		if (table.dealer_id && table.dealer_id.allowNull) {
			await queryInterface.changeColumn('tbl_dealer_files', 'dealer_id', {
				type: Sequelize.INTEGER,
				allowNull: false,
				references: {
					model: 'tbl_users',
					key: 'id',
				},
				onUpdate: 'CASCADE',
				onDelete: 'CASCADE',
			});
		}

		if (table.lead_id) {
			await queryInterface.removeColumn('tbl_dealer_files', 'lead_id');
		}
	},
};
