const express = require('express');

const router = express.Router();
const validate = require('../../middlewares/validate');
const { commonValidation } = require('../../validations');
const { commonController } = require('../../controllers');
const upload = require('../../middlewares/multer');
const { grantAccess } = require('../../middlewares/validateAccessControl');
const { resources } = require('../../config/roles');

router
	.route('/file')
	.post(
		grantAccess('create', 'any', resources.ADMIN_CONTROLS),
		upload.single('file'),
		validate(commonValidation.uploadFile),
		commonController.uploadFile
	)
	.get(
		grantAccess('create', 'any', resources.ADMIN_CONTROLS),
		commonController.getAllFiles
	);

router
	.route('/file/:fileName')
	.get(
		grantAccess('create', 'any', resources.ADMIN_CONTROLS),
		validate(commonValidation.getFileByName),
		commonController.getFilesByName
	)
	.delete(
		grantAccess('create', 'any', resources.ADMIN_CONTROLS),
		commonController.deleteFileByName
	);

module.exports = router;
