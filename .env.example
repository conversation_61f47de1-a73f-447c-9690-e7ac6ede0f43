NODE_ENV=development

PORT=4408

COOKIE_EXPIRATION_HOURS=24

JWT_SECRET=thisisasecretkey
JWT_ACCESS_EXPIRATION_MINUTES=30
JWT_REFRESH_EXPIRATION_DAYS=30

SQL_USERNAME=postgres
SQL_HOST=localhost
SQL_DATABASE_NAME=Winko
SQL_PASSWORD=123456
SQL_PORT=5432

# SMTP configuration options for the email service
# For testing, you can use a fake SMTP service like Ethereal: https://ethereal.email/create
SMTP_HOST=email-server
SMTP_PORT=587
SMTP_USERNAME=email-server-username
SMTP_PASSWORD=email-server-password
EMAIL_FROM=<EMAIL>

# PORT = 4407
# DB_USER = postgres
# DB_PASSWORD = 123456
# DB_HOST = localhost
# DB_PORT = 5432
# DB_NAME = Training
# SESSION_SECRET = secret

# AWS CONFIG
ACCESS_KEY_ID=aws-access-key-id
SECRET_ACCESS_KEY=aws-secret-access-key
REGION=aws-region
AWS_BUCKET_NAME=aws-s3-bucket-name

E_COM_BASE_URL=e-com-base-url
ERP_BASE_URL=erp-base-url