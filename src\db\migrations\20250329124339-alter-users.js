/** @type {import('sequelize-cli').Migration} */
module.exports = {
	up: async (queryInterface, Sequelize) => {
		const tableDescription = await queryInterface.describeTable('tbl_users');
		if (!tableDescription.job_title) {
			await queryInterface.addColumn('tbl_users', 'job_title', {
				type: Sequelize.STRING,
				allowNull: true,
			});
		}
	},

	down: async (queryInterface) => {
		const tableDescription = await queryInterface.describeTable('tbl_users');
		if (tableDescription.job_title) {
			await queryInterface.removeColumn('tbl_users', 'job_title');
		}
	},
};
