const jwt = require('jsonwebtoken');
const config = require('../config/config');

const authenticateUser = async (req, res, next) => {
	try {
		const authorised = req.headers.authorization;
		if (!authorised || !authorised.startsWith('Bearer ')) {
			return res.status(401).json({
				status: false,
				message: 'Unauthorised : No token is provided',
			});
		}
		const token = authorised.split(' ')[1];
		jwt.verify(token, config.jwt.secret, (err, decoded) => {
			if (err) {
				return res.status(401).json({
					status: false,
					message: 'Unauthorised: Invalid or expired token.',
				});
			}
			req.user = decoded;
			next();
		});
	} catch (error) {
		return res.status(500).json({
			status: false,
			message: 'Internal server error',
			data: error.message,
		});
	}
};

module.exports = authenticateUser;
