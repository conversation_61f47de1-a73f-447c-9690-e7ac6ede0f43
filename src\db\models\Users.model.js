const { Op } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
	const TblUsers = sequelize.define(
		'tbl_users',
		{
			id: {
				type: DataTypes.INTEGER,
				autoIncrement: true,
				primaryKey: true,
				allowNull: false,
			},
			first_name: {
				type: DataTypes.STRING(100),
				allowNull: false,
			},
			last_name: {
				type: DataTypes.STRING(100),
				allowNull: true,
			},
			mobile_no: {
				type: DataTypes.STRING(15),
				allowNull: true,
			},
			otp: {
				type: DataTypes.STRING(6),
				allowNull: true,
			},
			unique_id: {
				type: DataTypes.TEXT,
				allowNull: false,
			},
			profile_image: {
				type: DataTypes.STRING(400),
				allowNull: true,
			},
			profile_image_path: {
				type: DataTypes.TEXT,
				allowNull: true,
			},
			primary_email: {
				type: DataTypes.STRING(100),
				allowNull: false,
				unique: true,
			},
			password: {
				type: DataTypes.TEXT,
				allowNull: true,
			},
			login_pin: {
				type: DataTypes.STRING(60), // Length of 60 to store bcrypt hash
				allowNull: true,
				unique: true, // Making it unique to prevent duplicate PINs
				validate: {
					// Optional: Add validation if needed
					len: [60, 60], // Ensure the hashed PIN is exactly 60 characters
				},
			},
			token: {
				type: DataTypes.TEXT,
				allowNull: true,
			},
			device_token: {
				type: DataTypes.TEXT,
				allowNull: true,
			},
			wo_role_id: {
				type: DataTypes.SMALLINT,
				allowNull: false,
			},
			home_owner_id: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			dealer_id: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			two_step_verification: {
				type: DataTypes.BOOLEAN,
				defaultValue: false,
			},
			is_contact: {
				type: DataTypes.BOOLEAN,
				defaultValue: false,
			},
			login_access: {
				type: DataTypes.BOOLEAN,
				defaultValue: true,
			},
			is_active: {
				type: DataTypes.BOOLEAN,
				defaultValue: true,
			},

			created_by: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},

			modified_by: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			is_deleted: {
				type: DataTypes.BOOLEAN,
				defaultValue: false,
			},
			deleted_by: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			deleted_date: {
				type: DataTypes.DATE,
				allowNull: true,
			},
			dealer_role_id: {
				type: DataTypes.SMALLINT,
				allowNull: true,
			},
			resetPasswordToken: {
				type: DataTypes.STRING,
				allowNull: true,
			},
			resetPasswordExpires: {
				type: DataTypes.DATE,
				allowNull: true,
			},
			is_rejected: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: false,
			},
			is_blocked: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: false,
			},

			created_at: {
				type: DataTypes.DATE,
				allowNull: false,
				defaultValue: DataTypes.NOW,
			},
			updated_at: {
				type: DataTypes.DATE,
				allowNull: true,
				// defaultValue: DataTypes.NOW,
			},
			website: {
				type: DataTypes.STRING(255),
				allowNull: true,
			},
			company_name: {
				type: DataTypes.STRING(255),
				allowNull: true,
			},
			company_phone: {
				type: DataTypes.STRING(15),
				allowNull: true,
			},
			address: {
				type: DataTypes.STRING(500),
				allowNull: true,
			},
			city: {
				type: DataTypes.STRING(100),
				allowNull: true,
			},
			state: {
				type: DataTypes.STRING(100),
				allowNull: true,
			},
			country: {
				type: DataTypes.STRING(100),
				allowNull: true,
			},
			zip: {
				type: DataTypes.STRING(10),
				allowNull: true,
			},
			job_title: {
				type: DataTypes.STRING(10),
				allowNull: true,
			},
			registration_from: {
				// Add the new column here
				type: DataTypes.STRING(50),
				allowNull: true, // Set to false if you want to make it required
			},
			lead_status_id: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			lost_status_id: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			lost_status_comments: {
				type: DataTypes.TEXT,
				allowNull: true,
			},
			lead_source_id: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			lead_types_id: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			// owner_id:{
			//   type: DataTypes.INTEGER,
			//   allowNull: true,
			// },
			sales_rep: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			lead_name: {
				type: DataTypes.STRING,
				allowNull: true,
			},
			company_email: {
				type: DataTypes.STRING,
				allowNull: true,
			},
			address2: {
				type: DataTypes.TEXT,
				allowNull: true,
			},
			notes: {
				type: DataTypes.TEXT,
				allowNull: true,
			},
			social_id: {
				type: DataTypes.STRING(255),
				allowNull: true,
				comment: 'Social media platform user ID',
			},
			social_type: {
				type: DataTypes.ENUM('google', 'facebook'),
				allowNull: true,
				comment: 'Type of social media platform used for authentication',
			},

			activation_token: {
				type: DataTypes.STRING,
				allowNull: true,
			},
			activation_expires: {
				type: DataTypes.DATE,
				allowNull: true,
			},
			is_activation_verified: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: false,
				comment: 'true = verified, false = not verified',
			},
			dealer_homeowner: {
				type: DataTypes.BOOLEAN,
				allowNull: false,
				defaultValue: false,
			},
			markup: {
				type: DataTypes.FLOAT,
				allowNull: true,
				defaultValue: null,
			},
			taxable: {
				type: DataTypes.STRING,
				allowNull: true,
				defaultValue: null,
			},
		},
		{
			tableName: 'tbl_users',
			timestamps: true,
			paranoid: true,
			deletedAt: 'deleted_date',
			underscored: true,
		}
	);

	TblUsers.associate = function (models) {
		TblUsers.belongsTo(models.tbl_dealers, {
			foreignKey: 'dealer_id',
			targetKey: 'id',
			as: 'dealer', // Optional alias
		});
		TblUsers.belongsTo(models.tbl_home_owners, {
			foreignKey: 'home_owner_id',
			targetKey: 'id',
			as: 'homeOwner', // Optional alias
		});
		// ✅ Add missing association with tbl_wo_roles
		TblUsers.belongsTo(models.tbl_wo_roles, {
			foreignKey: 'wo_role_id',
			targetKey: 'id',
			as: 'role', // Must match the alias in `include`
		});

		// ✅ Add missing association with tbl_dealer_roles
		TblUsers.belongsTo(models.tbl_dealer_roles, {
			foreignKey: 'dealer_role_id',
			targetKey: 'id',
			as: 'dealer_role', // Must match the alias in `include`
		});

		TblUsers.hasMany(models.tbl_files, {
			foreignKey: 'dealer_id',
			sourceKey: 'dealer_id',
			as: 'dealer_files',
		});

		TblUsers.hasMany(models.tbl_social_links, {
			foreignKey: 'user_id',
			as: 'socialLinks',
		});

		TblUsers.hasOne(models.tbl_social_links, {
			foreignKey: 'user_id',
			as: 'socialLink',
		});

		TblUsers.belongsTo(models.tbl_users, {
			as: 'sales_rep_detail',
			foreignKey: 'sales_rep',
		});
		TblUsers.belongsTo(models.leadStatus, {
			as: 'status_detail',
			foreignKey: 'lead_status_id',
		});
		TblUsers.belongsTo(models.leadSource, {
			as: 'source_detail',
			foreignKey: 'lead_source_id',
		});
		TblUsers.belongsTo(models.leadTypes, {
			as: 'leads_types_detail',
			foreignKey: 'lead_types_id',
		});
		TblUsers.belongsTo(models.tbl_lead_lost_status, {
			as: 'leads_lost_status',
			foreignKey: 'lost_status_id',
		});
	};
	//  Hook to update `is_deleted` and `deleted_by` on soft delete
	TblUsers.addHook('beforeDestroy', async (role, options) => {
		// eslint-disable-next-line no-param-reassign
		role.is_deleted = true;
		// eslint-disable-next-line no-param-reassign
		role.deleted_by = options.deleted_by || null;
		await role.save(); // Save the update before deletion
	});

	// Hook to reset `is_deleted` and `deleted_by` on restore
	TblUsers.addHook('beforeRestore', async (role) => {
		// eslint-disable-next-line no-unused-expressions, no-param-reassign, no-sequences
		(role.is_deleted = false), (role.deleted_by = null);
		await role.save();
	});

	// static method to check if email is taken for dealer
	TblUsers.isDealerEmailTaken = async function (email, excludeUserId) {
		const user = await this.findOne({
			where: {
				primary_email: email,
				id: { [Op.ne]: excludeUserId }, // Exclude current user ID
			},
			raw: true,
		});

		return !!user;
	};

	// Pre-save hook for password hashing
	// TblUsers.beforeSave(async (user, options) => {
	//   if (user.changed('password')) {
	//     user.password = await bcrypt.hash(user.password, 10)
	//   }
	// });

	return TblUsers;
};
