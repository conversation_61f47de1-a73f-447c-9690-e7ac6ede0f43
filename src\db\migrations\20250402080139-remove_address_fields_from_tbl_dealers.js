/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up(queryInterface, Sequelize) {
		const tableDescription = await queryInterface.describeTable('tbl_dealers');

		const fieldsToRemove = [
			'city',
			'state',
			'country',
			'zip',
			'address',
			'modified_date',
			'created_date',
			'reason',
			'status',
			'email_id',
		];

		await Promise.all(
			fieldsToRemove.map(async (field) => {
				if (tableDescription[field]) {
					await queryInterface.removeColumn('tbl_dealers', field);
				} else {
					// console.log(`Column not found: ${field}`);
				}
			})
		);

		if (!tableDescription.created_at) {
			await queryInterface.addColumn('tbl_dealers', 'created_at', {
				type: Sequelize.DATE,
				allowNull: false,
				defaultValue: Sequelize.literal('NOW()'),
			});
		}

		if (!tableDescription.updated_at) {
			await queryInterface.addColumn('tbl_dealers', 'updated_at', {
				type: Sequelize.DATE,
				allowNull: false,
				defaultValue: Sequelize.literal('NOW()'),
			});
		}
	},

	async down(queryInterface, Sequelize) {
		const tableDescription = await queryInterface.describeTable('tbl_dealers');

		const fieldsToAdd = {
			city: { type: Sequelize.STRING },
			state: { type: Sequelize.STRING },
			country: { type: Sequelize.STRING },
			zip: { type: Sequelize.STRING },
			address: { type: Sequelize.STRING },
			modified_date: { type: Sequelize.DATE },
			created_date: { type: Sequelize.DATE },
			reason: { type: Sequelize.STRING },
			status: { type: Sequelize.STRING },
			email_id: { type: Sequelize.STRING },
		};
		await Promise.all(
			Object.entries(fieldsToAdd).map(async ([field, options]) => {
				if (!tableDescription[field]) {
					await queryInterface.addColumn('tbl_dealers', field, options);
				} else {
					// console.log(`Column already exists: ${field}`);
				}
			})
		);

		if (tableDescription.created_at) {
			await queryInterface.removeColumn('tbl_dealers', 'created_at');
		}

		if (tableDescription.updated_at) {
			await queryInterface.removeColumn('tbl_dealers', 'updated_at');
		}
	},
};
