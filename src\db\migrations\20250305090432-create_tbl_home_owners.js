/** @type {import('sequelize-cli').Migration} */
// c:\Users\<USER>\Desktop\your_project\migrations\20231010_create_tbl_home_owners.js

module.exports = {
	up: (queryInterface, Sequelize) =>
		queryInterface.createTable('tbl_home_owners', {
			id: {
				type: Sequelize.INTEGER,
				allowNull: false,
				primaryKey: true,
				autoIncrement: true,
			},
			first_name: {
				type: Sequelize.ARRAY(Sequelize.STRING(255)),
				allowNull: false,
			},
			last_name: {
				type: Sequelize.ARRAY(Sequelize.STRING(255)),
				allowNull: true,
			},
			mobile_no: {
				type: Sequelize.ARRAY(Sequelize.STRING(20)),
				allowNull: true,
			},
			email_id: {
				type: Sequelize.STRING(255),
				allowNull: false,
			},
			reason: {
				type: Sequelize.TEXT,
				allowNull: true,
			},
			status: {
				type: Sequelize.INTEGER,
				defaultValue: 0,
				allowNull: false,
			},
			created_by: {
				type: Sequelize.INTEGER,
				allowNull: false,
			},
			created_date: {
				type: Sequelize.DATE,
				defaultValue: Sequelize.NOW,
				allowNull: false,
			},
			modified_by: {
				type: Sequelize.INTEGER,
				allowNull: true,
			},
			modified_date: {
				type: Sequelize.DATE,
				allowNull: true,
			},
		}),
	down: (queryInterface /* , Sequelize */) =>
		queryInterface.dropTable('tbl_home_owners'),
};
