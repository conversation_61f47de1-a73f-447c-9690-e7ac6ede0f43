const catchAsync = require('../utils/catchAsync');
const { installerService } = require('../services');
const ApiSuccess = require('../utils/ApiSuccess');
const ApiError = require('../utils/ApiError');

const getInstallerList = catchAsync(async (req, res) => {
	const list_data = await installerService.getInstallerdata(req);
	return new ApiSuccess(
		res,
		200,
		'Successfully retrieved all record.',
		list_data
	);
});

const getInstallerListByid = catchAsync(async (req, res) => {
	const { id } = req.params;
	if (!id) {
		throw new ApiError(400, 'Installer ID is required.');
	}
	const installer = await installerService.getInstallerdataByid(id); // Fetch installer by ID

	if (!installer) {
		throw new ApiError(400, 'Installer not found');
	}
	return new ApiSuccess(
		res,
		200,
		'Successfully retrieved all record.',
		installer
	);
});

const addOrupdateInstaller = catchAsync(async (req, res) => {
	const { id, ...data } = req.body;
	let result;

	if (id) {
		// Update existing installer
		result = await installerService.updateInstaller(id, data);
		if (!result) {
			throw new ApiError(400, 'Record not found');
		}
	} else {
		// Create new installer
		result = await installerService.addInstaller(data);
	}
	return new ApiSuccess(
		res,
		200,
		'Installer added or updated successfully',
		result
	);
});

const deleteInstallerByid = catchAsync(async (req, res) => {
	const { id, userId } = req.body;
	let responses;
	if (id) {
		responses = await installerService.deleteInstaller(id, userId);
		if (!responses) {
			throw new ApiError(400, 'something went wrong');
		} else {
			return new ApiSuccess(res, 200, 'Installer delete successfully');
		}
	}
});

const exportToInstallerTable = catchAsync(async (req, res) => {
	const list_data = await installerService.getInstallerExport(req);
	// return new ApiSuccess(res, 200, 'Successfully retrieved all record.',list_data);
	await installerService.exportInstallerList(
		list_data,
		'Installer',
		'Installer.xlsx',
		res
	);
});

module.exports = {
	getInstallerList,
	addOrupdateInstaller,
	deleteInstallerByid,
	getInstallerListByid,
	exportToInstallerTable,
};
