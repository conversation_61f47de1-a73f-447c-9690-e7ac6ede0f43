/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up(queryInterface, Sequelize) {
		const tableDescription = await queryInterface.describeTable('tbl_banks');

		if (tableDescription.dealer_id.unique) {
			await queryInterface.changeColumn('tbl_banks', 'dealer_id', {
				type: Sequelize.INTEGER,
				allowNull: true,
				references: {
					model: 'tbl_dealers',
					key: 'id',
				},
				onDelete: 'CASCADE',
			});
		}

		if (!tableDescription.created_by) {
			await queryInterface.addColumn('tbl_banks', 'created_by', {
				type: Sequelize.INTEGER,
				allowNull: true,
			});
		}

		if (!tableDescription.modified_by) {
			await queryInterface.addColumn('tbl_banks', 'modified_by', {
				type: Sequelize.INTEGER,
				allowNull: true,
			});
		}

		if (!tableDescription.deleted_by) {
			await queryInterface.addColumn('tbl_banks', 'deleted_by', {
				type: Sequelize.INTEGER,
				allowNull: true,
			});
		}
	},

	async down(queryInterface, Sequelize) {
		const tableDescription = await queryInterface.describeTable('tbl_banks');

		if (!tableDescription.dealer_id.unique) {
			await queryInterface.changeColumn('tbl_banks', 'dealer_id', {
				type: Sequelize.INTEGER,
				allowNull: true,
				unique: true, // Restore the unique constraint if rolled back
				references: {
					model: 'tbl_dealers',
					key: 'id',
				},
				onDelete: 'CASCADE',
			});
		}

		if (tableDescription.created_by) {
			await queryInterface.removeColumn('tbl_banks', 'created_by');
		}

		// Remove modified_by column if it exists
		if (tableDescription.modified_by) {
			await queryInterface.removeColumn('tbl_banks', 'modified_by');
		}

		// Remove deleted_by column if it exists
		if (tableDescription.deleted_by) {
			await queryInterface.removeColumn('tbl_banks', 'deleted_by');
		}
	},
};
