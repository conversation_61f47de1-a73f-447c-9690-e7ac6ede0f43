const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const ApiError = require('../utils/ApiError');
const userService = require('../services/user.service');
const authService = require('../services/auth.service');
const { uploadSingleFileToAwsS3 } = require('../utils/awsFileUpload');
const ApiSuccess = require('../utils/ApiSuccess');

exports.getUserProfile = async (req, res) => {
	try {
		const { userId } = req.user; // Assuming the user ID is stored in req.user by the auth middleware
		// Fetch user data from tbl_users
		const user = await userService.getUserById(userId);
		if (!user) {
			return res.status(404).json({ error: 'User not found' });
		}
		const userData = {
			id: user.id,
			first_name: user.first_name,
			last_name: user.last_name,
			primary_email: user.primary_email,
			mobile_no: user.mobile_no,
			address: user.address,
			city: user.city,
			state: user.state,
			country: user.country,
			zip: user.zip,
			profile_image: user.profile_image,
		};
		if (req.user.roleId === 3) {
			// If the role is not Homeowner
			userData.website = user.website;
			userData.company_name = user.company_name;
			userData.company_phone = user.company_phone;
		}
		// Return the user data
		return res.status(200).json({
			status_code: 200,
			message: 'User profile fetched successfully',
			data: userData, // Return the complete user data
		});
	} catch (error) {
		return res.status(400).json({ error: error.message });
	}
};

// Helper function to extract user data
const extractUserData = (body, roleId) => {
	const {
		firstName,
		lastName,
		mobileNo,
		emailId,
		website,
		companyName,
		companyPhone,
		address,
		city,
		state,
		country,
		zip,
	} = body;

	const userData = {
		first_name: firstName,
		last_name: lastName,
		mobile_no: mobileNo,
		primary_email: emailId,
		address,
		city,
		state,
		country,
		zip,
	};

	// Include additional fields for non-homeowner roles
	if (roleId !== 2) {
		// If the role is not Homeowner
		userData.website = website;
		userData.company_name = companyName;
		userData.company_phone = companyPhone;
	}

	return userData;
};

// Helper function to update user role details
const updateUserRoleDetails = async (user, userData) => {
	if (user.wo_role_id === 2) {
		// Homeowner
		const { home_owner_id } = user;
		await userService.updateHomeowner(home_owner_id, {
			first_name: userData.first_name,
			last_name: userData.last_name,
			mobile_no: userData.mobile_no,
			email_id: userData.primary_email,
		});
	} else if (user.wo_role_id === 3) {
		// Dealer
		const { dealer_id } = user;
		await userService.updateDealer(dealer_id, {
			first_name: userData.first_name,
			last_name: userData.last_name,
			mobile_no: userData.mobile_no,
			email_id: userData.primary_email,
		});
	}
};

exports.updateProfile = async (req, res) => {
	try {
		const { id } = req.params; // Get id from the request parameters as userid
		const tokenUserId = req.user.userId; // User ID from the token
		const tokenRoleId = req.user.roleId; // Role ID from the token

		// Step 1: Authorize the user
		if (tokenUserId !== parseInt(id, 10)) {
			return res
				.status(403)
				.json({ error: 'You are not authorized to update this profile.' });
		}

		// Step 2: Extract user data from the request body
		const userData = extractUserData(req.body, tokenRoleId);

		// Step 3: Check for existing credentials
		await authService.checkExistingCredentials(
			userData.primary_email,
			userData.mobile_no,
			id,
			'update'
		);

		if (req.body.image_base64) {
			const uploadedImage = await uploadSingleFileToAwsS3({
				image: req.body.image_base64,
				document_name: `profile_image_${id}`,
				folder_name: 'profileImage',
			});
			userData.profile_image = uploadedImage.file_url; // Save the image URL to userData
		}

		// Step 4: Update user details in tbl_users
		await userService.updateUser(id, userData);

		// Step 5: Retrieve the updated user to check the role
		const updatedUser = await userService.getUserById(id);
		if (!updatedUser) {
			return res.status(404).json({ error: 'User not found' });
		}

		// Step 6: Update corresponding table based on user role
		await updateUserRoleDetails(updatedUser, userData);

		// Step 7: Return success response
		return res.status(200).json({
			status_code: 200,
			message: 'User information updated successfully',
		});
	} catch (error) {
		return res.status(400).json({ error: error.message });
	}
};

exports.createLoginPin = catchAsync(async (req, res) => {
	const { pin } = req.body;
	const { userId } = req.user; // Assuming user ID is available from auth middleware
	const result = await userService.createLoginPin(pin, userId);

	return new ApiSuccess(
		res,
		httpStatus.OK,
		'Login PIN created successfully',
		result
	);
});

exports.changePassword = catchAsync(async (req, res) => {
	try {
		// Get user ID from authenticated request
		const { userId } = req.user; // Access user from auth middleware

		if (!userId) {
			throw new ApiError(httpStatus.UNAUTHORIZED, 'Not authenticated');
		}

		const { currentPassword, newPassword } = req.body;

		// Process password change
		await userService.changePassword(userId, currentPassword, newPassword);

		// return res.status(200).json({
		//     success: true,
		//     message: 'Password changed successfully',
		//     ...result
		// });
		return new ApiSuccess(res, httpStatus.OK, 'Password Change Successfully!');
	} catch (error) {
		throw new ApiError(
			error.statusCode || httpStatus.INTERNAL_SERVER_ERROR,
			error.message || 'Error processing password change'
		);
	}
});
// const getUsers = catchAsync(async (req, res) => {
// 	const users = await userService.getUsers(req);
// 	res.send({ users });
// });

// const getUser = catchAsync(async (req, res) => {
// 	const user = await userService.getUserById(req.params.userId);

// 	if (!user) {
// 		throw new ApiError(httpStatus.NOT_FOUND, 'User not found');
// 	}

// 	delete user.password;
// 	res.send({ user });
// });

// const deleteUser = catchAsync(async (req, res) => {
// 	await userService.deleteUserById(req.params.userId);
// 	res.send({ success: true });
// });

// const updateUser = catchAsync(async (req, res) => {
// 	const user = await userService.updateUser(req);

// 	if (!user) {
// 		throw new ApiError(httpStatus.NOT_FOUND, 'User not found');
// 	}

// 	delete user.password;
// 	res.send({ user });
// });

// module.exports = {
// 	getUsers,
// 	getUser,
// 	updateUser,
// 	deleteUser,
// };
