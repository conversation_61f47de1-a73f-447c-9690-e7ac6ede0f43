/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up(queryInterface, Sequelize) {
		const tableDefinition = await queryInterface.describeTable('tbl_users');

		if (!tableDefinition.markup) {
			await queryInterface.addColumn('tbl_users', 'markup', {
				type: Sequelize.FLOAT,
				allowNull: true,
				defaultValue: null,
			});
		}

		if (!tableDefinition.taxable) {
			await queryInterface.addColumn('tbl_users', 'taxable', {
				type: Sequelize.STRING,
				allowNull: true, // allowNull must be true if default is null
				defaultValue: null,
			});
		}
	},

	async down(queryInterface) {
		const tableDefinition = await queryInterface.describeTable('tbl_users');

		if (tableDefinition.markup) {
			await queryInterface.removeColumn('tbl_users', 'markup');
		}

		if (tableDefinition.taxable) {
			await queryInterface.removeColumn('tbl_users', 'taxable');
		}
	},
};
