/** @type {import('sequelize-cli').Migration} */
module.exports = {
	up: async (queryInterface, Sequelize) => {
		const tableDescription = await queryInterface.describeTable('tbl_users');

		if (!tableDescription.lead_status_id) {
			await queryInterface.addColumn('tbl_users', 'lead_status_id', {
				type: Sequelize.INTEGER,
				allowNull: true,
			});
		}

		if (!tableDescription.lead_source_id) {
			await queryInterface.addColumn('tbl_users', 'lead_source_id', {
				type: Sequelize.INTEGER,
				allowNull: true,
			});
		}

		if (!tableDescription.lead_types_id) {
			await queryInterface.addColumn('tbl_users', 'lead_types_id', {
				type: Sequelize.INTEGER,
				allowNull: true,
			});
		}

		if (!tableDescription.owner_id) {
			await queryInterface.addColumn('tbl_users', 'owner_id', {
				type: Sequelize.INTEGER,
				allowNull: true,
			});
		}

		if (!tableDescription.sales_rep) {
			await queryInterface.addColumn('tbl_users', 'sales_rep', {
				type: Sequelize.INTEGER,
				allowNull: true,
			});
		}

		if (!tableDescription.lead_name) {
			await queryInterface.addColumn('tbl_users', 'lead_name', {
				type: Sequelize.STRING,
				allowNull: true,
			});
		}

		if (!tableDescription.company_email) {
			await queryInterface.addColumn('tbl_users', 'company_email', {
				type: Sequelize.STRING,
				allowNull: true,
			});
		}

		if (!tableDescription.address2) {
			await queryInterface.addColumn('tbl_users', 'address2', {
				type: Sequelize.TEXT,
				allowNull: true,
			});
		}

		if (!tableDescription.notes) {
			await queryInterface.addColumn('tbl_users', 'notes', {
				type: Sequelize.TEXT,
				allowNull: true,
			});
		}
	},

	down: async (queryInterface) => {
		const tableDescription = await queryInterface.describeTable('tbl_users');

		if (tableDescription.lead_status_id) {
			await queryInterface.removeColumn('tbl_users', 'lead_status_id');
		}

		if (tableDescription.lead_source_id) {
			await queryInterface.removeColumn('tbl_users', 'lead_source_id');
		}

		if (tableDescription.lead_types_id) {
			await queryInterface.removeColumn('tbl_users', 'lead_types_id');
		}

		if (tableDescription.owner_id) {
			await queryInterface.removeColumn('tbl_users', 'owner_id');
		}

		if (tableDescription.lead_name) {
			await queryInterface.removeColumn('tbl_users', 'lead_name');
		}

		if (tableDescription.company_email) {
			await queryInterface.removeColumn('tbl_users', 'company_email');
		}

		if (tableDescription.address2) {
			await queryInterface.removeColumn('tbl_users', 'address2');
		}

		if (tableDescription.notes) {
			await queryInterface.removeColumn('tbl_users', 'notes');
		}
	},
};
