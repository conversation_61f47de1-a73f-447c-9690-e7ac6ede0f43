const Joi = require('@hapi/joi');

const createNote = {
	body: Joi.object()
		.keys({
			dealer_id: Joi.number().optional(),
			lead_id: Joi.number().optional(),
			title: Joi.string().required(),
			description: Joi.string().allow(null, ''),
			labels: Joi.alternatives().try(
				Joi.array().items(Joi.number().integer()),
				Joi.valid(null, '')
			),
			files: Joi.array()
				.items(
					Joi.object({
						document_name: Joi.string().required().min(1),
						image: Joi.string().required().min(1),
					})
				)
				.optional()
				.default([]),
		})
		.xor('dealer_id', 'lead_id'),
};

const getNotesByDealerId = {
	params: Joi.object().keys({
		dealerId: Joi.number().integer().positive().required(),
	}),
	query: Joi.object().keys({
		page: Joi.number().integer().positive().default(1),
		limit: Joi.number().integer().positive().default(10),
		search: Joi.string().allow(null, '').optional(),
	}),
};

const getNoteById = {
	params: Joi.object().keys({
		noteId: Joi.number().integer().positive().required(),
	}),
};

const updateNoteById = {
	params: Joi.object().keys({
		noteId: Joi.number().integer().positive().required(),
	}),
	body: Joi.object().keys({
		title: Joi.string().required(),
		description: Joi.string().allow(null, ''),
		labels: Joi.alternatives().try(
			Joi.array().items(Joi.number().integer()),
			Joi.valid(null, '')
		),
		files: Joi.array()
			.items(
				Joi.object({
					id: Joi.string().uuid().optional(),
					file_url: Joi.string().uri().optional(),
					document_name: Joi.string().optional(),
					image: Joi.string().optional(),
				})
			)
			.optional()
			.default([]),
	}),
};

const getNotesByLeadId = {
	params: Joi.object().keys({
		leadId: Joi.number().integer().positive().required(),
	}),
	query: Joi.object().keys({
		page: Joi.number().integer().positive().default(1),
		limit: Joi.number().integer().positive().default(10),
		search: Joi.string().allow(null, '').optional(),
	}),
};

module.exports = {
	createNote,
	getNotesByDealerId,
	getNoteById,
	updateNoteById,
	getNotesByLeadId,
};
