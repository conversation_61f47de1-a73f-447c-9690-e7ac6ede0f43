const httpStatus = require('http-status');
const xlsx = require('xlsx');
const db = require('../db/models');
const ApiError = require('../utils/ApiError');

async function getInstallerdata(req) {
	const { page = 1, limit = 10 } = req.query;
	const offset = (page - 1) * limit;

	const installerData = await db.Installer.findAndCountAll({
		where: { is_deleted: false },
		limit: parseInt(limit, 10),
		offset: parseInt(offset, 10),
		raw: true,
	});

	if (!installerData) {
		throw new ApiError(httpStatus.NOT_FOUND, 'No installers found');
	}

	return installerData;
}
async function getInstallerExport() {
	const installerData = await db.Installer.findAll({
		where: { is_deleted: false },
		raw: true,
	});

	if (!installerData) {
		throw new ApiError(httpStatus.NOT_FOUND, 'No installers found');
	}

	return installerData;
}

async function getInstallerdataByid(id) {
	const installerData = await db.Installer.findByPk(id);
	if (!installerData) {
		throw new ApiError(httpStatus.NOT_FOUND, 'No installers found');
	}

	return installerData;
}

async function addInstaller(data) {
	return db.Installer.create({ ...data, is_deleted: false });
}

async function updateInstaller(id, data) {
	const [updated] = await db.Installer.update(data, {
		where: { id, is_deleted: false },
		returning: true,
	});

	if (!updated) {
		throw new ApiError(httpStatus.NOT_FOUND, 'Installer not found');
	}

	return db.Installer.findByPk(id);
}

async function deleteInstaller(id, userId) {
	if (!id) {
		throw new Error('Installer ID is required.');
	}
	const isInstallerExist = await db.Installer.findByPk(id);
	if (!isInstallerExist) {
		throw new ApiError(httpStatus.NOT_FOUND, 'Installer not found');
	}

	// Perform soft delete
	const [deleteData] = await db.Installer.update(
		{ is_deleted: true, deleted_by: userId, deleted_date: new Date() },
		{
			where: { id },
			returning: true,
		}
	);

	if (!deleteData) {
		throw new ApiError(httpStatus.NOT_FOUND, 'Installer not found');
	}

	return true;
}

async function exportInstallerList(data, sheetName, fileName, res) {
	// Create workbook and worksheet
	const workbook = xlsx.utils.book_new();

	const headers = {
		name: 'Full Name',
		email: 'Email',
		phone: 'Mobile No',
		dealer_id: 'Dealer id',
		status: 'Status',
	};

	const formattedData = data.map((item) => {
		const newItem = {};

		Object.keys(headers).forEach((key) => {
			newItem[headers[key]] = item[key] || '';
		});
		return newItem;
	});

	const worksheet = xlsx.utils.json_to_sheet(formattedData);
	xlsx.utils.book_append_sheet(workbook, worksheet, sheetName);

	res.setHeader(
		'Content-Type',
		'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
	);
	res.setHeader('Content-Disposition', `attachment; filename=${fileName}`);
	const stream = xlsx.write(workbook, { type: 'buffer', bookType: 'xlsx' });

	return res.send(stream);
}

module.exports = {
	getInstallerdata,
	addInstaller,
	updateInstaller,
	getInstallerdataByid,
	deleteInstaller,
	getInstallerExport,
	exportInstallerList,
};
