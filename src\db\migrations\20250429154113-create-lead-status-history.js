/** @type {import('sequelize-cli').Migration} */
module.exports = {
	up: async (queryInterface, Sequelize) => {
		await queryInterface.createTable(
			'tbl_lead_status_history',
			{
				id: {
					type: Sequelize.INTEGER,
					autoIncrement: true,
					primaryKey: true,
					allowNull: false,
				},
				lead_id: {
					type: Sequelize.INTEGER,
					allowNull: false,
					references: {
						model: 'tbl_users', // name of the target table
						key: 'id', // key in the target table that we're referencing
					},
					onUpdate: 'CASCADE',
					onDelete: 'SET NULL',
				},
				lead_status_id: {
					type: Sequelize.INTEGER,
					allowNull: false,
					references: {
						model: 'tbl_lead_status',
						key: 'id',
					},
					onUpdate: 'CASCADE',
					onDelete: 'SET NULL',
				},
				created_at: {
					type: Sequelize.DATE,
					allowNull: false,
				},
				created_by: {
					type: Sequelize.INTEGER,
					allowNull: true,
				},
			},
			{
				tableName: 'tbl_lead_status_history',
			}
		);
	},

	down: async (queryInterface) => {
		await queryInterface.dropTable('tbl_lead_status_history');
	},
};
