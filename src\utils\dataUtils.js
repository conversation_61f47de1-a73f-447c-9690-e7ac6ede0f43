/**
 * Dynamically builds an update object with only defined fields.
 * @param {Object} source - The source object (e.g., request body)
 * @param {string[]} fields - Array of allowed fields
 * @returns {Object}
 */
const buildUpdateObject = (source, fields) => {
	return fields.reduce((acc, field) => {
		if (source[field] !== undefined) {
			acc[field] = source[field];
		}
		return acc;
	}, {});
};

/**
 * Flattens a Sequelize user instance with an optional dealer association.
 * @param {Object} user - Sequelize instance with dealer relation
 * @returns {Object} Flattened plain object
 */
const flattenUserDealer = (user) => {
	const userPlain = user.get({ plain: true });
	const dealerPlain = user.dealer?.get ? user.dealer.get({ plain: true }) : {};
	return { ...userPlain, ...dealerPlain };
};

module.exports = {
	buildUpdateObject,
	flattenUserDealer,
};
