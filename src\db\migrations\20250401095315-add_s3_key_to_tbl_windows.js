/** @type {import('sequelize-cli').Migration} */
module.exports = {
	up: async (queryInterface, Sequelize) => {
		const tableDescription = await queryInterface.describeTable('tbl_windows');
		if (!tableDescription.s3_key) {
			await queryInterface.addColumn('tbl_windows', 's3_key', {
				type: Sequelize.STRING,
				allowNull: true, // Adjust according to your needs
			});
		}
	},

	down: async (queryInterface) => {
		const tableDescription = await queryInterface.describeTable('tbl_windows');
		if (tableDescription.s3_key) {
			await queryInterface.removeColumn('tbl_windows', 's3_key');
		}
	},
};
