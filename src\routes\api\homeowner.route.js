const express = require('express');
const multer = require('multer');
const homeownerController = require('../../controllers/homeowner.controller');
const { grantAccess } = require('../../middlewares/validateAccessControl');
const { resources } = require('../../config/roles');
const validate = require('../../middlewares/validate');
const homeownerValidation = require('../../validations/homeowner.validation');

const router = express.Router();

/**
 * @swagger
 * components:
 *   responses:
 *     InternalServerError:
 *       description: Internal server error
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: boolean
 *                 example: false
 *               message:
 *                 type: string
 *                 example: "An unexpected error occurred"
 */

// Configure multer for file upload
const upload = multer({
	storage: multer.memoryStorage(),
	limits: {
		fileSize: 5 * 1024 * 1024, // 5MB limit
	},
	fileFilter: (req, file, cb) => {
		if (
			file.mimetype === 'application/vnd.ms-excel' ||
			file.mimetype ===
				'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
			file.mimetype === 'text/csv'
		) {
			cb(null, true);
		} else {
			cb(new Error('Only Excel/CSV files are allowed'));
		}
	},
});

router.post(
	'/import',
	grantAccess('create', 'any', resources.HOMEOWNER),
	upload.single('file'),
	validate(homeownerValidation.importHomeowners),
	homeownerController.importHomeowners
);

router.get(
	'/export',
	grantAccess('read', 'any', resources.HOMEOWNER),
	homeownerController.exportHomeownerList
); // Add the new route
// List route with validation
router.get(
	'/list',
	grantAccess('read', 'any', resources.HOMEOWNER),
	validate(homeownerValidation.getHomeownerList), // Add validation back

	homeownerController.getHomeownerList
);

/**
 * @swagger
 * /homeowner/list:
 *   get:
 *     summary: Get a list of homeowners
 *     description: Retrieve a list of homeowners with pagination, filtering, and sorting.
 *     tags: [Homeowner]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Maximum number of results per page
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Current page number
 *       - in: query
 *         name: order_by
 *         schema:
 *           type: string
 *           enum: [id, first_name, last_name, primary_email, mobile_no, created_at, updated_at]
 *           default: created_at
 *         description: Field to sort by
 *       - in: query
 *         name: sort
 *         schema:
 *           type: string
 *           enum: [asc, desc, ASC, DESC]
 *           default: DESC
 *         description: Sort order
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term for name, email, or mobile
 *       - in: query
 *         name: status
 *         schema:
 *           type: integer
 *           enum: [0, 1]
 *         description: Filter by status (0 for active,1 for inactive)
 *     responses:
 *       200:
 *         description: A list of homeowners with pagination metadata
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: integer
 *                   example: 200
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Homeowner list retrieved successfully"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                       first_name:
 *                         type: string
 *                       last_name:
 *                         type: string
 *                       primary_email:
 *                         type: string
 *                       mobile_no:
 *                         type: string
 *                       status:
 *                         type: string
 *                       created_at:
 *                         type: string
 *                         format: date-time
 *                       updated_at:
 *                         type: string
 *                         format: date-time
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     totalRows:
 *                       type: integer
 *                       example: 100
 *                     filteredRows:
 *                       type: integer
 *                       example: 10
 *                     currentPage:
 *                       type: integer
 *                       example: 1
 *                     totalPages:
 *                       type: integer
 *                       example: 10
 *                     nextPage:
 *                       type: integer
 *                       example: 2
 *                       nullable: true
 *                     prevPage:
 *                       type: integer
 *                       example: null
 *                       nullable: true
 *                     hasNextPage:
 *                       type: boolean
 *                       example: true
 *                     hasPrevPage:
 *                       type: boolean
 *                       example: false
 *       401:
 *         description: Unauthorized - Invalid or missing token
 *       403:
 *         description: Forbidden - User doesn't have the required permissions
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /homeowner/{id}:
 *   get:
 *     summary: Get a homeowner by ID
 *     description: Retrieve detailed information about a specific homeowner
 *     tags: [Homeowner]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Homeowner ID
 *     responses:
 *       200:
 *         description: Homeowner data
 *       401:
 *         description: Unauthorized - Invalid or missing token
 *       403:
 *         description: Forbidden - User doesn't have the required permissions
 *       404:
 *         description: Homeowner not found
 *       500:
 *         description: Internal server error
 */

/**
 * @swagger
 * /homeowner/export:
 *   get:
 *     summary: Export a list of homeowners
 *     description: Retrieve a list of homeowners and export it in CSV format.
 *     tags: [Homeowner]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Homeowner data exported successfully
 *         content:
 *           text/csv:
 *             schema:
 *               type: string
 *               example: |
 *                 id,first_name,last_name,primary_email,mobile_no,status,created_at,updated_at
 *                 1,John,Doe,<EMAIL>,1234567890,active,2023-01-01T00:00:00Z,2023-01-01T00:00:00Z
 *                 2,Jane,Smith,<EMAIL>,0987654321,inactive,2023-01-02T00:00:00Z,2023-01-02T00:00:00Z
 *       401:
 *         description: Unauthorized - Invalid or missing token
 *       403:
 *         description: Forbidden - User doesn't have the required permissions
 *       500:
 *         description: Internal server error
 */

// Single homeowner route with validation
router.get(
	'/:id',
	grantAccess('read', 'any', resources.HOMEOWNER),
	validate(homeownerValidation.getHomeownerById), // Add validation back

	homeownerController.getHomeownerById
);

/**
 * @swagger
 * /homeowner/{id}:
 *   delete:
 *     summary: Soft delete a homeowner
 *     description: |
 *       Soft delete a homeowner by updating is_deleted flag.
 *
 *       **Important Notes:**
 *       1. This is a soft delete operation
 *       2. Updates is_deleted, deleted_by and deleted_at in the database
 *       3. Requires authentication and proper permissions
 *     tags: [Homeowner]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: User ID to delete
 *     responses:
 *       "200":
 *         description: Homeowner deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Homeowner deleted successfully"
 *       "404":
 *         description: Homeowner not found
 *       "403":
 *         description: Forbidden - Insufficient permissions
 */
router.delete(
	'/:id',
	grantAccess('delete', 'any', resources.HOMEOWNER),
	validate(homeownerValidation.deleteHomeowner),
	homeownerController.deleteHomeowner
);

/**
 * @swagger
 * /homeowner:
 *   post:
 *     summary: Create a new homeowner
 *     description: |
 *       Creates a new homeowner account and sends login credentials via email.
 *
 *       **Important Notes:**
 *       1. The request must include the homeowner's first name, last name, email, and mobile number.
 *       2. If the request is made by an admin (roleId 1), the activation token and password will be generated and sent to the homeowner.
 *       3. If the request is made by a dealer (roleId 3), the homeowner will be created without sending sensitive information.
 *
 *     tags: [Homeowner]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - first_name
 *               - last_name
 *               - primary_email
 *               - mobile_no
 *             properties:
 *               first_name:
 *                 type: string
 *                 example: "John"
 *               last_name:
 *                 type: string
 *                 example: "Doe"
 *               primary_email:
 *                 type: string
 *                 format: email
 *                 example: "<EMAIL>"
 *               mobile_no:
 *                 type: string
 *                 pattern: '^\d{10}$'
 *                 example: "1234567890"
 *               address:
 *                 type: string
 *                 example: "123 Example St"
 *               city:
 *                 type: string
 *                 example: "Example City"
 *               state:
 *                 type: string
 *                 example: "Example State"
 *               country:
 *                 type: string
 *                 example: "Example Country"
 *               zip:
 *                 type: string
 *                 example: "12345"
 *     responses:
 *       201:
 *         description: Homeowner created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Homeowner created successfully. Login credentials have been sent to their email."
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                     first_name:
 *                       type: string
 *                     last_name:
 *                       type: string
 *                     primary_email:
 *                       type: string
 *                     mobile_no:
 *                       type: string
 *                     address:
 *                       type: string
 *                     city:
 *                       type: string
 *                     state:
 *                       type: string
 *                     country:
 *                       type: string
 *                     zip:
 *                       type: string
 *                     created_at:
 *                       type: string
 *                       format: date-time
 *       400:
 *         description: Validation Error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Validation error"
 *                 errors:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       field:
 *                         type: string
 *                       message:
 *                         type: string
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       403:
 *         $ref: '#/components/responses/Forbidden'
 *       409:
 *         description: Email already exists
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Email already exists"
 *       500:
 *         $ref: '#/components/responses/InternalServerError'
 */
router.post(
	'/',
	grantAccess('create', 'any', resources.HOMEOWNER),
	validate(homeownerValidation.createHomeowner),
	homeownerController.addHomeowner
);

/**
 * @swagger
 * /homeowner/{id}:
 *   patch:
 *     summary: Update a homeowner
 *     description: Update an existing homeowner's information
 *     tags: [Homeowner]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: Homeowner ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               first_name:
 *                 type: string
 *                 example: "John"
 *               last_name:
 *                 type: string
 *                 example: "Doe"
 *               primary_email:
 *                 type: string
 *                 format: email
 *                 example: "<EMAIL>"
 *               mobile_no:
 *                 type: string
 *                 pattern: '^\d{10}$'
 *                 example: "1234567890"
 *               address:
 *                 type: string
 *                 example: "123 Example St"
 *               city:
 *                 type: string
 *                 example: "Example City"
 *               state:
 *                 type: string
 *                 example: "Example State"
 *               country:
 *                 type: string
 *                 example: "Example Country"
 *               zip:
 *                 type: string
 *                 example: "12345"
 *     responses:
 *       200:
 *         description: Homeowner updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Homeowner updated successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                     first_name:
 *                       type: string
 *                     last_name:
 *                       type: string
 *                     primary_email:
 *                       type: string
 *                     mobile_no:
 *                       type: string
 *                     address:
 *                       type: string
 *                     city:
 *                       type: string
 *                     state:
 *                       type: string
 *                     country:
 *                       type: string
 *                     zip:
 *                       type: string
 *                     updated_at:
 *                       type: string
 *                       format: date-time
 *       400:
 *         description: Validation Error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Validation error"
 *                 errors:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       field:
 *                         type: string
 *                       message:
 *                         type: string
 *       401:
 *         $ref: '#/components/responses/Unauthorized'
 *       403:
 *         $ref: '#/components/responses/Forbidden'
 *       404:
 *         description: Homeowner not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Homeowner not found"
 *       409:
 *         description: Email already exists
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Email already exists"
 *       500:
 *         $ref: '#/components/responses/InternalServerError'
 */
router.patch(
	'/:id',
	grantAccess('update', 'any', resources.HOMEOWNER),
	validate(homeownerValidation.updateHomeowner),
	homeownerController.updateHomeowner
);

/**
 * @swagger
 * /homeowner/import:
 *   post:
 *     summary: Import homeowners from CSV/Excel file
 *     description: |
 *       Upload a CSV or Excel file containing homeowner information for bulk import.
 *
 *       **Supported File Formats:**
 *       - CSV (.csv)
 *       - Excel (.xls, .xlsx)
 *
 *       **Required Columns:**
 *       - first_name
 *       - primary_email
 *       - mobile_no
 *
 *       **Optional Columns:**
 *       - last_name
 *       - address
 *       - city
 *       - state
 *       - country
 *       - zip
 *
 *       **File Size Limit:** 5MB
 *
 *       **Example CSV Format:**
 *       ```
 *       first_name,last_name,primary_email,mobile_no,address,city,state,country,zip
 *       John,Doe,<EMAIL>,1234567890,123 Main St,New York,NY,USA,12345
 *       Jane,Smith,<EMAIL>,0987654321
 *       ```
 *
 *       Note: Address fields (address, city, state, country, zip) are optional.
 *       If provided in the CSV, they will be saved in the user's profile.
 *     tags: [Homeowner]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - file
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: CSV or Excel file containing homeowner data
 *     responses:
 *       200:
 *         description: Import completed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: integer
 *                   example: 200
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Homeowners import completed: 2 successful, 1 failed"
 *                 data:
 *                   type: object
 *                   properties:
 *                     total:
 *                       type: integer
 *                       example: 3
 *                     successful:
 *                       type: integer
 *                       example: 2
 *                     failed:
 *                       type: integer
 *                       example: 1
 *                     failures:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           row:
 *                             type: integer
 *                           error:
 *                             type: string
 *                           data:
 *                             type: object
 *                     imported:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                           first_name:
 *                             type: string
 *                           email_id:
 *                             type: string
 *                           mobile_no:
 *                             type: string
 *                           address:
 *                             type: string
 *                           city:
 *                             type: string
 *                           state:
 *                             type: string
 *                           country:
 *                             type: string
 *                           zip:
 *                             type: string
 */
// router.post(
//     '/import',
//     grantAccess('create', 'any', resources.HOMEOWNER),
//     upload.single('file'),
//     validate(homeownerValidation.importHomeowners),
//     homeownerController.importHomeowners
// );
module.exports = router;
