const { Op } = require('sequelize');
const moment = require('moment');

const getDateFilter = (date) => {
	const now = moment().endOf('day');

	// eslint-disable-next-line default-case
	switch (date) {
		case 'today':
			return {
				[Op.gte]: moment().startOf('day').toDate(),
				[Op.lte]: now.toDate(),
			};

		case 'last_7_days':
			return {
				[Op.gte]: moment().subtract(7, 'days').startOf('day').toDate(),
				[Op.lte]: now.toDate(),
			};
		case 'this_month':
			return {
				[Op.gte]: moment().startOf('month').toDate(),
				[Op.lte]: now.toDate(),
			};
		case 'last_12_months':
			return {
				[Op.gte]: moment().subtract(12, 'months').startOf('day').toDate(),
				[Op.lte]: now.toDate(),
			};
		case 'all_time':
			return null;
	}
};

module.exports = {
	getDateFilter,
};
