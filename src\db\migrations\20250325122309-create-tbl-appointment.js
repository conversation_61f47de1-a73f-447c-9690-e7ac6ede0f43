/** @type {import('sequelize-cli').Migration} */
module.exports = {
	async up(queryInterface, Sequelize) {
		await queryInterface.createTable('tbl_appointments', {
			id: {
				allowNull: false,
				autoIncrement: true,
				primaryKey: true,
				type: Sequelize.INTEGER,
			},
			title: {
				type: Sequelize.TEXT,
				allowNull: false,
			},
			description: {
				type: Sequelize.TEXT,
				allowNull: true,
			},
			start_date: {
				type: Sequelize.DATE,
				allowNull: false,
			},
			start_time: {
				type: Sequelize.TIME,
				allowNull: false,
			},
			meeting_place: {
				type: Sequelize.TEXT,
				allowNull: true,
			},
			created_by: {
				type: Sequelize.INTEGER,
				allowNull: false,
				references: {
					model: 'tbl_wo_roles',
					key: 'id',
				},
				onDelete: 'CASCADE',
				onUpdate: 'CASCADE',
			},
			dealer_id: {
				type: Sequelize.INTEGER,
				allowNull: true,
				references: {
					model: 'tbl_dealers',
					key: 'id',
				},
				onDelete: 'CASCADE',
				onUpdate: 'CASCADE',
			},
			color: {
				type: Sequelize.STRING,
				allowNull: true,
				defaultValue: '#aab7b7',
			},
			files: {
				type: Sequelize.JSONB, // Use JSONB for PostgreSQL, JSON for other DBs
				allowNull: true,
				defaultValue: [],
				validate: {
					isValidFileArray(value) {
						if (value && !Array.isArray(value)) {
							throw new Error('File must be an array of objects');
						}
						if (
							value &&
							value.some(
								(file) =>
									!file.file_name ||
									!file.file_url ||
									!file.file_type ||
									!file.file_key ||
									!file.file_size ||
									!file.id
							)
						) {
							throw new Error(
								'Each file must have file_name, file_url, file_type, file_key,file_size, and id'
							);
						}
					},
				},
			},

			type: {
				type: Sequelize.ENUM('offline', 'online'),
				allowNull: false,
			},
			specific_member_employee: {
				type: Sequelize.ARRAY(Sequelize.INTEGER),
				allowNull: true,
				default: [],
			},
			reminder: {
				type: Sequelize.ARRAY(Sequelize.STRING),
				allowNull: true,
				defaultValue: null,
				validate: {
					isValidReminder(value) {
						if (value && !Array.isArray(value)) {
							throw new Error('Reminder must be an array');
						}
						if (
							value &&
							value.some(
								(item) => !['30_min', '1_hour', '1_day'].includes(item)
							)
						) {
							throw new Error('Invalid reminder value');
						}
					},
				},
			},
			created_at: {
				allowNull: false,
				type: Sequelize.DATE,
			},
			updated_at: {
				allowNull: false,
				type: Sequelize.DATE,
			},
		});

		// creating enum from repeat_type
		await queryInterface.sequelize.query(`
      DO $$ 
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'enum_tbl_appointments_repeat_type') THEN
          CREATE TYPE "enum_tbl_appointments_repeat_type" AS ENUM ('days', 'weeks', 'months', 'years');
        END IF;
      END $$;
    `);
	},
	async down(queryInterface) {
		await queryInterface.dropTable('tbl_appointments');

		// drop the ENUM type
		await queryInterface.sequelize.query(`
      DO $$ 
      BEGIN
        IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'enum_tbl_appointments_repeat_type') THEN
          DROP TYPE "enum_tbl_appointments_repeat_type";
        END IF;
      END $$;
    `);
	},
};
