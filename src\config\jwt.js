const { expressjwt } = require('express-jwt');
const config = require('./config');
// const { FlatESLint } = require('eslint/use-at-your-own-risk');

// async function isRevoked(req, payload, done) {
// 	done();
// }

async function isRevoked() {
	return false; // No token revocation logic for now
}

function jwt() {
	const { secret } = config.jwt;
	return expressjwt({
		secret,
		algorithms: ['HS256'],
		getToken: function fromHeaderOrQuerystring(req) {
			const token = req.headers.authorization
				? req.headers.authorization.split(' ')[1]
				: req.query.token;
			return token || null;
		},
		isRevoked,
	}).unless({
		path: [
			// public routes that don't require authentication
			'/api/auth/login',
			'/api/auth/register',
			'/api/dealers/register',
			'/api/dealers/verify-otp',
			'/api/auth/forgot-password',
			'/api/auth/reset-password',
			// you can add more public paths here
			/\/api(\d)*\/(admin)\/.*/,
			{
				url: /^\/api\/appointments/,
				methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
			},
			/\/api(\d)*\/(auth|docs)\/.*/,
		],
	});
}

module.exports = jwt;
