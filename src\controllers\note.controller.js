const httpStatus = require('http-status');
const ApiSuccess = require('../utils/ApiSuccess');
const catchAsync = require('../utils/catchAsync');
const { noteService, dealerService, leadsService } = require('../services');
const RESPONSE = require('../utils/ApiResponseMsg');

/**
 * Create a new note.
 * @param {number} dealer_id - Dealer ID.
 * @param {string} title - Note title.
 * @param {string} [description] - Note description.
 * @param {string[]} [labels] - Note labels.
 * @param {Object[]} [files] - Attached files.
 * @returns {Promise<void>}
 */
const createNote = catchAsync(async (req, res) => {
	const { userId } = req.user;
	const { dealer_id, lead_id } = req.body;

	req.body.created_by = userId;

	if (dealer_id) {
		await dealerService.getDealerById(dealer_id);
	} else {
		await leadsService.getLeadById(lead_id);
	}

	const note = await noteService.createNote(req.body);
	const { files: _, ...noteData } = note;

	const noteRes = {
		...noteData,
		files: note.files.map((file) => ({
			id: file.id,
			file_url: file.file_url,
			file_name: file.file_name,
			file_type: file.file_type,
		})),
	};

	return new ApiSuccess(
		res,
		httpStatus.OK,
		RESPONSE.CREATE_SUCCESS('note'),
		noteRes
	);
});

/**
 * Get all notes by dealer ID.
 * @param {number} dealerId - Dealer ID.
 * @param {number} page - Page number.
 * @param {number} limit - Items per page.
 * @param {string} [search] - Search keyword.
 * @returns {Promise<void>}
 */
const getNotesByDealerId = catchAsync(async (req, res) => {
	const { dealerId } = req.params;
	const { page, limit, search } = req.query;

	await dealerService.getDealerById(dealerId);

	const { notes, pagination } = await noteService.getAllNotesByDealerId(
		dealerId,
		page,
		limit,
		search
	);
	return new ApiSuccess(
		res,
		httpStatus.OK,
		RESPONSE.FETCH_SUCCESS('notes'),
		notes,
		{ pagination }
	);
});

/**
 * Get note by ID.
 * @param {number} noteId - Note ID.
 * @returns {Promise<void>}
 */
const getNoteById = catchAsync(async (req, res) => {
	const { noteId } = req.params;

	const note = await noteService.getNoteById(noteId);

	const { files: _, ...noteData } = note;

	const noteRes = {
		...noteData, // Include all other fields except files
		files: note.files.map((file) => ({
			id: file.id,
			file_url: file.file_url,
			file_name: file.file_name,
			file_type: file.file_type,
		})),
	};

	return new ApiSuccess(
		res,
		httpStatus.OK,
		RESPONSE.FETCH_SUCCESS('note'),
		noteRes
	);
});

/**
 * Update note by ID.
 * @param {number} noteId - Note ID.
 * @param {Object} updateBody - Updated note data.
 * @returns {Promise<void>}
 */
const updateNoteById = catchAsync(async (req, res) => {
	const { userId } = req.user;
	const { noteId } = req.params;

	await noteService.updateNoteById(noteId, req.body, userId);

	return new ApiSuccess(res, httpStatus.OK, RESPONSE.UPDATE_SUCCESS('note'));
});

/**
 * Delete note by ID.
 * @param {number} noteId - Note ID.
 * @returns {Promise<void>}
 */
const deleteNoteById = catchAsync(async (req, res) => {
	const { userId } = req.user;
	const { noteId } = req.params;
	await noteService.deleteNoteById(noteId, userId);

	return new ApiSuccess(res, httpStatus.OK, RESPONSE.DELETE_SUCCESS('note'));
});

/**
 * Get all notes by dealer ID.
 * @param {number} leadId - Dealer ID.
 * @param {number} page - Page number.
 * @param {number} limit - Items per page.
 * @param {string} [search] - Search keyword.
 * @returns {Promise<void>}
 */
const getNotesByLeadId = catchAsync(async (req, res) => {
	const { leadId } = req.params;
	const { page, limit, search } = req.query;

	await leadsService.getLeadById(leadId);

	const { rows, pagination } = await noteService.getAllNotesByLeadId(
		leadId,
		page,
		limit,
		search
	);
	return new ApiSuccess(
		res,
		httpStatus.OK,
		RESPONSE.FETCH_SUCCESS('notes'),
		rows,
		{ pagination }
	);
});

module.exports = {
	createNote,
	getNotesByDealerId,
	getNoteById,
	updateNoteById,
	deleteNoteById,
	getNotesByLeadId,
};
