const express = require('express');
const swaggerJsdoc = require('swagger-jsdoc');
const swaggerUi = require('swagger-ui-express');
const swaggerDefinition = require('../../docs/swaggerDef');

const router = express.Router();

// Generate initial swagger specs
swaggerJsdoc({
	swaggerDefinition,
	apis: ['src/docs/*.yml', 'src/routes/api/*.js'],
});

// Serve swagger UI
router.use('/', swaggerUi.serve);

// Setup swagger with dynamic URL
router.get('/', (req, res, next) => {
	// Update the server URL based on the current request
	swaggerDefinition.updateServerUrl(req);

	// Regenerate specs with updated URL
	const updatedSpecs = swaggerJsdoc({
		swaggerDefinition,
		apis: ['src/docs/*.yml', 'src/routes/api/*.js'],
	});

	// Log for debugging
	// eslint-disable-next-line no-console
	console.log('Current Swagger URL:', swaggerDefinition.servers[0].url);

	// Setup swagger UI with updated specs
	swaggerUi.setup(updatedSpecs, {
		explorer: true,
	})(req, res, next);
});

// Optional: Endpoint to get raw swagger JSON with current URL
router.get('/swagger.json', (req, res) => {
	swaggerDefinition.updateServerUrl(req);
	const updatedSpecs = swaggerJsdoc({
		swaggerDefinition,
		apis: ['src/docs/*.yml', 'src/routes/api/*.js'],
	});
	res.json(updatedSpecs);
});

module.exports = router;
