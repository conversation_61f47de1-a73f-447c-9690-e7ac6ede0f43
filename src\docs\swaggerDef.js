const { version } = require('../../package.json');
const config = require('../config/config');

const swaggerDef = {
	openapi: '3.0.0',
	info: {
		title: 'Winco API documentation',
		version,
		license: {
			name: '',
			url: '',
		},
	},
	servers: [
		{
			url: '{protocol}://{host}/api',
			variables: {
				protocol: {
					enum: ['http', 'https'],
					default: 'http',
				},
				host: {
					default: process.env.HOST || `localhost:${config.port}`,
				},
			},
			description: 'Dynamic Server URL',
		},
	],
};

// Helper function to update server URL dynamically
swaggerDef.updateServerUrl = function (req) {
	const protocol = req.headers['x-forwarded-proto'] || req.protocol;
	const host = req.headers['x-forwarded-host'] || req.get('host');

	this.servers[0].url = `${protocol}://${host}/api`;
};

module.exports = swaggerDef;
