module.exports = (sequelize, DataTypes) => {
	const TblRooms = sequelize.define(
		'tbl_rooms',
		{
			id: {
				type: DataTypes.INTEGER,
				autoIncrement: true,
				primaryKey: true,
				allowNull: false,
			},
			user_id: {
				type: DataTypes.INTEGER, // Changed from STRING to INTEGER as per updated requirements
				allowNull: false,
			},
			room_name: {
				type: DataTypes.STRING(255),
				allowNull: false,
			},
			is_delete: {
				type: DataTypes.BOOLEAN,
				defaultValue: false,
			},
			created_by: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			created_date: {
				type: DataTypes.DATE,
				defaultValue: DataTypes.NOW,
				allowNull: false,
			},
			updated_date: {
				type: DataTypes.DATE,
				//  defaultValue: DataTypes.NOW,
				allowNull: true,
			},
			deleted_by: {
				type: DataTypes.INTEGER,
				allowNull: true,
			},
			deleted_at: {
				type: DataTypes.DATE,
				allowNull: true,
			},
		},
		{
			tableName: 'tbl_rooms',
			timestamps: false,
			paranoid: true,
			deletedAt: 'deleted_at',
		}
	);

	TblRooms.associate = function (models) {
		TblRooms.hasMany(models.tbl_windows, {
			foreignKey: 'room_id',
			sourceKey: 'id',
			as: 'windows', // Optional alias
		});

		TblRooms.belongsTo(models.tbl_users, {
			foreignKey: 'user_id',
			targetKey: 'id',
			as: 'user', // Optional alias
		});
	};

	return TblRooms;
};
